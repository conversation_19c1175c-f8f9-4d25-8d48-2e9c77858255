namespace Paytently.Core.Rules.Engine.Benchmarker.Models;

public record Payment
{
    public required string Id { get; init; }
    public required string Status { get; init; }
    public int Amount { get; init; }
    public required string Currency { get; init; }
    public Method? Method { get; init; }
    public Customer? Customer { get; init; }
}

public enum MethodType
{
    Card,
    OpenBanking
}

public record Method
{
    public int? Id { get; init; }
    public required MethodType Type { get; init; }
    public Card? Card { get; init; }
    public OpenBanking? OpenBanking { get; init; }
}

public record OpenBanking
{
    public string? AccountNumber { get; init; }
}

public record Card
{
    public required string Number { get; init; }
    public string? Scheme { get; init; }
    public Issuer? Issuer { get; init; }
}

public record Customer
{
    public required string FirstName { get; init; }
    public required string LastName { get; init; }
    public required Contact Contact { get; init; }
}

public record Contact
{
    public required string Email { get; init; }
}

public record Phone
{
    public required string CountryCode { get; init; }
}

public sealed record Issuer
{
    public string? Name { get; init; }

    public string? Country { get; init; }
}
