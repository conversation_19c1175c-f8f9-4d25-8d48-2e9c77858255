using BenchmarkDotNet.Attributes;
using Microsoft.Extensions.DependencyInjection;
using Paytently.Core.Rules.Engine;
using Paytently.Core.Rules.Engine.Benchmarker.Models;
using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Benchmarker;

[MemoryDiagnoser]
public class CacheBenchmarks
{
    IRulesEngine<Payment> _rulesEngine;
    Payment _fact;
    Rule _nonCachedRule;
    Rule _cachedRule;

    public CacheBenchmarks()
    {
        ServiceCollection cachedServices = new();
        cachedServices.AddRulesEngine(
            new EngineConfiguration()
            {
                CacheConfiguration = new CacheConfiguration()
                {
                    Expiration = TimeSpan.FromMinutes(5)
                }
            }
        );
        ServiceProvider provider = cachedServices.BuildServiceProvider();
        _rulesEngine = provider.GetRequiredService<IRulesEngine<Payment>>();
        _fact = new()
        {
            Id = "1",
            Status = "pending",
            Amount = 3000,
            Currency = "USD",
            Customer = new Customer
            {
                FirstName = "John",
                LastName = "Doe",
                Contact = new Contact { Email = "<EMAIL>" }
            }
        };
        _nonCachedRule = new()
        {
            Id = Guid.NewGuid().ToString(),
            Conditions =
            [
                new AllowBlockCondition
                {
                    Property = "Amount",
                    Operator = Engine.Constants.Operator.EqualsTo,
                    Values = ["3000"],
                    PropertyType = Engine.Constants.PropertyTypes.Integer
                },
                new AllowBlockCondition
                {
                    Property = "Customer.Contact.Email",
                    Operator = Engine.Constants.Operator.EqualsTo,
                    Values = ["<EMAIL>"],
                    PropertyType = Engine.Constants.PropertyTypes.String
                }
            ]
        };
        _cachedRule = new()
        {
            Id = Guid.NewGuid().ToString(),
            Conditions =
            [
                new AllowBlockCondition
                {
                    IntegrityHash = Guid.NewGuid().ToString(),
                    Property = "Amount",
                    Operator = Engine.Constants.Operator.EqualsTo,
                    Values = ["3000"],
                    PropertyType = Engine.Constants.PropertyTypes.Integer
                },
                new AllowBlockCondition
                {
                    IntegrityHash = Guid.NewGuid().ToString(),
                    Property = "Customer.Contact.Email",
                    Operator = Engine.Constants.Operator.EqualsTo,
                    Values = ["<EMAIL>"],
                    PropertyType = Engine.Constants.PropertyTypes.String
                }
            ]
        };
    }

    [Benchmark]
    public void EvaluateCached()
    {
        _rulesEngine.Evaluate(_cachedRule, _fact);
    }

    [Benchmark]
    public void EvaluateNonCached()
    {
        _rulesEngine.Evaluate(_nonCachedRule, _fact);
    }
}
