<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <SonarQubeExclude>true</SonarQubeExclude>

    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="BenchmarkDotNet" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\Paytently.Core.Rules.Engine\Paytently.Core.Rules.Engine.csproj" />
    </ItemGroup>

</Project>
