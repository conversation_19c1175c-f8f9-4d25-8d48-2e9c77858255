name: Release and publish nuget

permissions:
  actions: write
  checks: write
  pull-requests: write
  contents: write
  packages: write

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  release:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: DOTNET build nuget pack push and release
        uses: paytently/core-github-actions/dotnet-build-pack-and-push-nuget-package@v1
        with:
          nuget-api-key: "${{ secrets.GH_NUGET_API_KEY }}"
