name: Release and publish nuget

permissions:
  actions: write
  checks: write
  pull-requests: write
  contents: write
  packages: write

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  release:
    uses: paytently/core-github-actions/.github/workflows/dotnet-test-publish-release-nuget-package.yml@v1
    secrets:
      nuget-api-key: ${{ secrets.GH_NUGET_API_KEY }}
      ptly_artifacts_api_key: ${{ secrets.PTLY_ARTIFACTS_API_KEY }}
