name: Pull Request

permissions:
  contents: write
  checks: write

on:
  workflow_dispatch:
  pull_request:
    branches: [ main ]
  push: 
    branches: [ main ]

jobs:
  pull_request:
    uses: paytently/core-github-actions/.github/workflows/dotnet-pull-request.yml@v1
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      sonar_token: ${{ secrets.SONAR_TOKEN }}
      ptly_artifacts_api_key: ${{ secrets.PTLY_ARTIFACTS_API_KEY }}
    