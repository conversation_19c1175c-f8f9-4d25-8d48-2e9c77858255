root=true
; EditorConfig helps developers define and maintain consistent
; coding styles between different editors and IDEs.

; For more visit http://editorconfig.org.

; Choose between lf or rf on "end_of_line" property
[*.proto]
indent_style=tab
indent_size=tab
tab_width=4

[*.{asax,ascx,aspx,cs,cshtml,css,htm,html,js,jsx,master,razor,skin,ts,tsx,vb,xaml,xamlx,xoml}]
indent_style=space
indent_size=4
tab_width=4

[*.{appxmanifest,build,config,csproj,dbml,discomap,dtd,json,jsproj,lsproj,njsproj,nuspec,proj,props,resjson,resw,resx,StyleCop,targets,tasks,vbproj,xml,xsd}]
indent_style=space
indent_size=2
tab_width=2

[*]

# Standard properties
end_of_line=lf
insert_final_newline=true

# Microsoft .NET properties
csharp_indent_braces=false
csharp_indent_switch_labels=true
csharp_new_line_before_catch=true
csharp_new_line_before_else=true
csharp_new_line_before_finally=true
csharp_new_line_before_members_in_object_initializers=false
csharp_new_line_before_open_brace=all
csharp_new_line_between_query_expression_clauses=true
csharp_preferred_modifier_order=public, private, protected, internal, new, abstract, virtual, sealed, override, static, readonly, extern, unsafe, volatile, async:suggestion
csharp_preserve_single_line_blocks=true
csharp_space_after_cast=true
csharp_space_after_colon_in_inheritance_clause=true
csharp_space_after_comma=true
csharp_space_after_dot=false
csharp_space_after_keywords_in_control_flow_statements=true
csharp_space_after_semicolon_in_for_statement=true
csharp_space_around_binary_operators=before_and_after
csharp_space_before_colon_in_inheritance_clause=true
csharp_space_before_comma=false
csharp_space_before_dot=false
csharp_space_before_open_square_brackets=false
csharp_space_before_semicolon_in_for_statement=false
csharp_space_between_empty_square_brackets=false
csharp_space_between_method_call_empty_parameter_list_parentheses=false
csharp_space_between_method_call_name_and_opening_parenthesis=false
csharp_space_between_method_call_parameter_list_parentheses=false
csharp_space_between_method_declaration_empty_parameter_list_parentheses=false
csharp_space_between_method_declaration_name_and_open_parenthesis=false
csharp_space_between_method_declaration_parameter_list_parentheses=false
csharp_space_between_parentheses=false
csharp_space_between_square_brackets=false
csharp_style_expression_bodied_accessors=true:suggestion
csharp_style_expression_bodied_constructors=true:suggestion
csharp_style_expression_bodied_methods=true:suggestion
csharp_style_expression_bodied_properties=true:suggestion
csharp_style_var_elsewhere=false:error
csharp_style_var_for_built_in_types=false:error
csharp_style_var_when_type_is_apparent=false:error
csharp_using_directive_placement=outside_namespace:silent
dotnet_style_predefined_type_for_locals_parameters_members=true:hint
dotnet_style_predefined_type_for_member_access=true:hint
dotnet_style_qualification_for_event=false:hint
dotnet_style_qualification_for_field=false:hint
dotnet_style_qualification_for_method=false:hint
dotnet_style_qualification_for_property=false:hint
dotnet_style_require_accessibility_modifiers=for_non_interface_members:hint
dotnet_diagnostic.RS0062.severity = error
dotnet_public_api_analyzer.require_api_files = true