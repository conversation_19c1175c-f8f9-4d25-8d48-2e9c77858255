
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{D990B0C3-3C7C-42CB-BA1E-9F904222EAB5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "solution-items", "solution-items", "{2CD8392C-57CA-40B9-B284-B42B730E40F7}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		.editorconfig = .editorconfig
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		nuget.config = nuget.config
		.github\workflows\pull_request.yml = .github\workflows\pull_request.yml
		.github\workflows\release.yml = .github\workflows\release.yml
		.github\CODEOWNERS = .github\CODEOWNERS
		.github\pull_request_template.md = .github\pull_request_template.md
		.gitignore = .gitignore
		.husky\task-runner.json = .husky\task-runner.json
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Paytently.Core.Rules.Engine", "src\Paytently.Core.Rules.Engine\Paytently.Core.Rules.Engine.csproj", "{568FF911-2CD2-4A04-897D-BEF814547F55}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{09A0DC5B-35C4-4CB2-918A-54FA53138303}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Paytently.Core.Rules.Engine.Unit.Tests", "tests\Paytently.Core.Rules.Engine.Unit.Tests\Paytently.Core.Rules.Engine.Unit.Tests.csproj", "{95D2EEDC-7EB0-42AA-94CA-7E9D0DF62B84}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Paytently.Core.Rules.Engine.Contracts", "src\Paytently.Core.Rules.Engine.Contracts\Paytently.Core.Rules.Engine.Contracts.csproj", "{D2119FF6-F6B6-44CC-90F8-0E0DACB8AB9A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{568FF911-2CD2-4A04-897D-BEF814547F55} = {D990B0C3-3C7C-42CB-BA1E-9F904222EAB5}
		{95D2EEDC-7EB0-42AA-94CA-7E9D0DF62B84} = {09A0DC5B-35C4-4CB2-918A-54FA53138303}
		{D2119FF6-F6B6-44CC-90F8-0E0DACB8AB9A} = {D990B0C3-3C7C-42CB-BA1E-9F904222EAB5}
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{568FF911-2CD2-4A04-897D-BEF814547F55}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{568FF911-2CD2-4A04-897D-BEF814547F55}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{568FF911-2CD2-4A04-897D-BEF814547F55}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{568FF911-2CD2-4A04-897D-BEF814547F55}.Release|Any CPU.Build.0 = Release|Any CPU
		{95D2EEDC-7EB0-42AA-94CA-7E9D0DF62B84}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95D2EEDC-7EB0-42AA-94CA-7E9D0DF62B84}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95D2EEDC-7EB0-42AA-94CA-7E9D0DF62B84}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95D2EEDC-7EB0-42AA-94CA-7E9D0DF62B84}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2119FF6-F6B6-44CC-90F8-0E0DACB8AB9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2119FF6-F6B6-44CC-90F8-0E0DACB8AB9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2119FF6-F6B6-44CC-90F8-0E0DACB8AB9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2119FF6-F6B6-44CC-90F8-0E0DACB8AB9A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
