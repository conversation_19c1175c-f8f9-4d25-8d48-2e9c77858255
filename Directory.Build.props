<Project>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <!-- Enable .NET code analysis (this is necessary for projects that target 3.1 or earlier). -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <Nullable>enable</Nullable>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
  
  <PropertyGroup>
    <NuGetAuditMode>all</NuGetAuditMode>
    <NuGetAuditLevel>low</NuGetAuditLevel>
    <NoWarn>FS2003;FS0044;RS0016;RS0037;NU5104</NoWarn>
    <WarningsAsErrors>NU1901;NU1902;NU1903;NU1904</WarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Roslyn.Diagnostics.Analyzers">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>
</Project>
