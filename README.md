# Paytently Core Rules Engine

A flexible and extensible rules engine for .NET applications that allows you to define and evaluate business rules against any type of data object.

## Overview

The Paytently Core Rules Engine provides a simple yet powerful way to define business rules and evaluate them against your domain objects. It supports conditional logic with various operators and can be used for scenarios like payment validation, fraud detection, access control, and more.

## Features

- 🎯 **Type-safe rule evaluation** - Generic interface supports any data type
- 🔧 **Flexible condition system** - Support for multiple operators and property paths
- 🚀 **Easy dependency injection** - Built-in service collection extensions
- ⚡ **High-performance evaluation** - Uses FastExpressionCompiler for optimized rule execution
- 📊 **Detailed results** - Returns matching and non-matching conditions for analysis

## Installation

Install the NuGet packages using your preferred method:

### .NET CLI
```bash
# For contracts only
dotnet add package Paytently.Core.Rules.Engine.Contracts

# For full implementation
dotnet add package Paytently.Core.Rules.Engine
```

## Quick Start

### 1. Register the Rules Engine

Add the rules engine to your dependency injection container with configuration:

```csharp
using Paytently.Core.Rules.Engine;

// In your Program.cs or Startup.cs
services.AddRulesEngine();
```

You can also specify a custom types resolution if needed (e.g., for enums as property of complex objects):

```csharp
services.AddRulesEngine(new EngineConfiguration
{
    CustomTypes = [typeof(MyFirstEnum), typeof(MySecondEnum)],
});
```

### 2. Define Your Data Model

```csharp
public record Payment
{
    public required string Id { get; init; }
    public required string Status { get; init; }
    public int Amount { get; init; }
    public required string Currency { get; init; }
    public Customer? Customer { get; init; }
}

public record Customer
{
    public required string FirstName { get; init; }
    public required string LastName { get; init; }
    public required Contact Contact { get; init; }
}

public record Contact
{
    public required string Email { get; init; }
}
```

### 3. Create and Evaluate Rules

```csharp
using Paytently.Core.Rules.Engine.Contracts;

public class PaymentService
{
    private readonly IRulesEngine<Payment> _rulesEngine;

    public PaymentService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
    }

    public bool ValidatePayment(Payment payment)
    {
        // Define a rule to block high-value payments
        var highValueRule = new Rule
        {
            Id = "high-value-block",
            Name = "Block High Value Payments",
            Type = Type.Block,
            Conditions = new[]
            {
                new Condition
                {
                    Property = "Amount",
                    Operator = Operator.GreaterThan,
                    Values = new[] { "10000" }
                }
            }
        };

        // Evaluate the rule
        Result result = _rulesEngine.Evaluate(highValueRule, payment);
        
        // For Block rules, IsMatch = true means the rule matched (should block)
        // For Allow rules, IsMatch = true means the rule matched (should allow)
        return rule.Type == Type.Block ? !result.IsMatch : result.IsMatch;
    }
}
```

## Core Concepts

### Rule

A `Rule` represents a business rule that can be evaluated against a data object.

```csharp
public record Rule
{
    public required string Id { get; init; }        // Unique identifier
    public required string Name { get; init; }      // Human-readable name
    public required Type Type { get; init; }        // Block or Allow
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}
```

### Type

Defines the rule type:

```csharp
public enum Type
{
    Block,  // Rule blocks/prevents action when conditions are met
    Allow   // Rule allows action when conditions are met
}
```

### Condition

A `Condition` defines a specific check within a rule:

```csharp
public record Condition
{
    public required string Property { get; init; } = "[self]";  // Property path to evaluate
    public required Operator Operator { get; init; }           // Comparison operator
    public required string[] Values { get; init; }             // Values to compare against
}
```

### Operator

Supported operators for conditions:

```csharp
public enum Operator
{
    Equals,
    NotEquals,
    Contains,
    Between
}
```

The `Between` operator requires exactly two values.

You can have your model to override IComparable<> to allow comparisons.
The following operators must be implemented
==, !=, <, >, <=, >=

You must define a Contains method for the Contains operator.

### Result

The evaluation result with detailed information:

```csharp
public record Result
{
    public bool IsMatch { get; init; }                                    // Whether all conditions matched
    public required IReadOnlyCollection<Condition> MatchingConditions { get; init; }    // Conditions that matched
    public required IReadOnlyCollection<Condition> NonMatchingConditions { get; init; } // Conditions that didn't match
}
```

## Usage Examples

### Example 1: Simple Property Check

```csharp
// Block payments with "failed" status
var failedPaymentRule = new Rule
{
    Id = "block-failed",
    Name = "Block Failed Payments",
    Type = Type.Block,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Status",
            Operator = Operator.Equals,
            Values = new[] { "failed" }
        }
    }
};

var payment = new Payment
{
    Id = "pay_123",
    Status = "failed",
    Amount = 1000,
    Currency = "USD"
};

Result result = _rulesEngine.Evaluate(failedPaymentRule, payment);
// result.IsMatch will be true (rule matched - should block)
// result.MatchingConditions will contain the status condition
```

### Example 2: Multiple Conditions

```csharp
// Block high-value USD payments
var highValueUsdRule = new Rule
{
    Id = "high-usd-block",
    Name = "Block High Value USD Payments",
    Type = Type.Block,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Amount",
            Operator = Operator.GreaterThan,
            Values = new[] { "5000" }
        },
        new Condition
        {
            Property = "Currency",
            Operator = Operator.Equals,
            Values = new[] { "USD" }
        }
    }
};

// All conditions must match for IsMatch = true
```

### Example 3: Nested Property Access

```csharp
// Block payments from specific email domains
var emailDomainRule = new Rule
{
    Id = "block-domain",
    Name = "Block Suspicious Email Domains",
    Type = Type.Block,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Customer.Contact.Email",
            Operator = Operator.Contains,
            Values = new[] { "@suspicious.com" }
        }
    }
};
```

### Example 4: Allow Rules

```csharp
// Allow payments only from verified customers
var verifiedCustomerRule = new Rule
{
    Id = "allow-verified",
    Name = "Allow Verified Customers",
    Type = Type.Allow,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Customer.IsVerified",
            Operator = Operator.Equals,
            Values = new[] { "true" }
        }
    }
};
```

## API Reference

### IRulesEngine<T>

The main interface for rule evaluation:

```csharp
public interface IRulesEngine<in T>
{
    public Result Evaluate(Rule rule, T fact);
}
```

**Parameters:**
- `rule`: The rule to evaluate
- `fact`: The data object to evaluate against

**Returns:** `Result` object with evaluation details

### ServiceCollectionExtensions

Extension methods for dependency injection:

```csharp
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(
        this IServiceCollection services,
        EngineConfiguration configuration
    );
}
```

**Parameters:**
- `configuration`: Engine configuration with condition translator type

## Advanced Usage


### Rule Evaluation Service with Detailed Results

```csharp
public class PaymentRuleService
{
    private readonly IRulesEngine<Payment> _rulesEngine;
    private readonly List<Rule> _rules;

    public PaymentRuleService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
        _rules = LoadRulesFromDatabase(); // Your rule storage
    }

    public PaymentValidationResult ValidatePayment(Payment payment)
    {
        var result = new PaymentValidationResult { IsValid = true };

        foreach (var rule in _rules)
        {
            Result evaluationResult = _rulesEngine.Evaluate(rule, payment);
            
            var ruleResult = new RuleEvaluationResult
            {
                RuleId = rule.Id,
                RuleName = rule.Name,
                IsMatch = evaluationResult.IsMatch,
                MatchingConditions = evaluationResult.MatchingConditions?.ToList() ?? new(),
                NonMatchingConditions = evaluationResult.NonMatchingConditions?.ToList() ?? new()
            };

            result.RuleResults.Add(ruleResult);
            
            if (evaluationResult.IsMatch)
            {
                if (rule.Type == Type.Block)
                {
                    result.IsValid = false;
                    result.BlockedByRules.Add(rule.Id);
                }
                else if (rule.Type == Type.Allow)
                {
                    result.AllowedByRules.Add(rule.Id);
                }
            }
        }

        return result;
    }
}

public class PaymentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> BlockedByRules { get; set; } = new();
    public List<string> AllowedByRules { get; set; } = new();
    public List<RuleEvaluationResult> RuleResults { get; set; } = new();
}

public class RuleEvaluationResult
{
    public required string RuleId { get; set; }
    public required string RuleName { get; set; }
    public bool IsMatch { get; set; }
    public List<Condition> MatchingConditions { get; set; } = new();
    public List<Condition> NonMatchingConditions { get; set; } = new();
}
```

### Testing Rules and Translators

```csharp

[Test]
public void RulesEngine_Should_Block_High_Value_Payments()
{
    // Arrange
    var services = new ServiceCollection();
    services.AddRulesEngine(new EngineConfiguration
    {
        ConditionTranslatorType = typeof(PaymentConditionTranslator)
    });
    
    var provider = services.BuildServiceProvider();
    var rulesEngine = provider.GetRequiredService<IRulesEngine<Payment>>();
    
    var rule = new Rule
    {
        Id = "test-rule",
        Name = "Test High Value Block",
        Type = Type.Block,
        Conditions = new[]
        {
            new Condition
            {
                Property = "Amount",
                Operator = Operator.GreaterThan,
                Values = new[] { "1000" }
            }
        }
    };
    
    var payment = new Payment
    {
        Id = "test",
        Status = "pending",
        Amount = 1500,
        Currency = "USD"
    };

    // Act
    Result result = rulesEngine.Evaluate(rule, payment);

    // Assert
    Assert.That(result.IsMatch, Is.True);
    Assert.That(result.MatchingConditions, Has.Count.EqualTo(1));
    Assert.That(result.NonMatchingConditions, Is.Null.Or.Empty);
}
```

## Requirements

- **.NET 8.0** or later

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Development Setup

1. Clone the repository
2. Ensure you have .NET 8.0 SDK installed
3. Run `dotnet restore` to restore dependencies
4. Run `dotnet build` to build the solution
5. Run `dotnet test` to execute tests

### Code Style

This project uses:
- **C# 12** language features
- **Nullable reference types** enabled
- **ImplicitUsings** enabled
- **CSharpier** for code formatting

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or contributions, please visit the [GitHub repository](https://github.com/paytently/core-dotnet-authentication).
