# Paytently Core Rules Engine

A flexible and extensible rules engine for .NET applications that allows you to define and evaluate business rules against any type of data object.

## Overview

The Paytently Core Rules Engine provides a simple yet powerful way to define business rules and evaluate them against your domain objects. It supports conditional logic with various operators, nested conditions, and can be used for scenarios like payment validation, fraud detection, access control, and more.

## Features

- 🎯 **Type-safe rule evaluation** - Generic interface supports any data type
- 🔧 **Flexible condition system** - Support for multiple operators and property paths
- 🚀 **Easy dependency injection** - Built-in service collection extensions
- 📦 **Two NuGet packages** - Contracts for interfaces, Engine for implementation
- ⚡ **High-performance evaluation** - Uses FastExpressionCompiler for optimized rule execution
- 🔌 **Nested conditions** - Support for complex logical structures with AND/OR operations
- 📊 **Detailed results** - Returns matching and non-matching conditions for analysis
- 🏷️ **Custom type support** - Built-in support for enums and custom types
- 🚀 **Intelligent caching** - Optional condition-level caching with integrity hash validation
- ⏱️ **Configurable cache expiration** - Customizable sliding expiration for cached expressions

## Installation

Install the NuGet packages using your preferred method:

### Package Manager Console
```powershell
# For contracts only (if you only need interfaces)
Install-Package Paytently.Core.Rules.Engine.Contracts

# For full implementation
Install-Package Paytently.Core.Rules.Engine
```

### .NET CLI
```bash
# For contracts only
dotnet add package Paytently.Core.Rules.Engine.Contracts

# For full implementation
dotnet add package Paytently.Core.Rules.Engine
```

### PackageReference
```xml
<PackageReference Include="Paytently.Core.Rules.Engine.Contracts" Version="*" />
<PackageReference Include="Paytently.Core.Rules.Engine" Version="*" />
```

## Quick Start

### 1. Register the Rules Engine

Add the rules engine to your dependency injection container:

```csharp
using Paytently.Core.Rules.Engine;

// In your Program.cs or Startup.cs
services.AddRulesEngine();
```

You can also specify custom types for enum resolution and enable caching:

```csharp
services.AddRulesEngine(new EngineConfiguration
{
    CustomTypes = [typeof(PaymentStatus), typeof(MethodType)],
    CacheConfiguration = new CacheConfiguration
    {
        Expiration = TimeSpan.FromMinutes(10) // Default is 5 minutes
    }
});
```

### 2. Define Your Data Model

```csharp
public record Payment
{
    public required string Id { get; init; }
    public required string Status { get; init; }
    public int Amount { get; init; }
    public required string Currency { get; init; }
    public Customer? Customer { get; init; }
    public Method? Method { get; init; }
}

public record Customer
{
    public required string FirstName { get; init; }
    public required string LastName { get; init; }
    public required Contact Contact { get; init; }
}

public record Contact
{
    public required string Email { get; init; }
}

public record Method
{
    public required MethodType Type { get; init; }
}

public enum MethodType
{
    Card,
    OpenBanking
}
```

### 3. Create and Evaluate Rules

```csharp
using Paytently.Core.Rules.Engine.Contracts;

public class PaymentService
{
    private readonly IRulesEngine<Payment> _rulesEngine;

    public PaymentService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
    }

    public bool ValidatePayment(Payment payment)
    {
        // Define a rule to block high-value payments
        PaymentRule highValueRule = new PaymentRule
        {
            Id = "high-value-block",
            Conditions = new[]
            {
                new PaymentCondition
                {
                    Property = new Property
                    {
                        Property = "Amount",
                        Operator = "GreaterThan",
                        Values = new[] { "10000" },
                        PropertyType = "Integer"
                    }
                }
            }
        };

        // Evaluate the rule
        Result result = _rulesEngine.Evaluate(highValueRule, payment);
        
        // result.IsMatch = true means the rule conditions matched
        return !result.IsMatch; // Return true if payment is valid (not blocked)
    }
}

// Implementation of IRule for your domain
public record PaymentRule : IRule
{
    public string? LogicalOperator { get; init; } = "and"; // Default to AND logic
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}

// Implementation of Condition for simple property checks
public record PaymentCondition : Condition
{
    // Property is inherited from base Condition class
    // Conditions is inherited for nested conditions (can be null for simple conditions)
    // IntegrityHash is inherited for caching support (optional)
}
```

## Core Concepts

### IRule

The `IRule` interface represents a business rule that can be evaluated against a data object:

```csharp
public interface IRule
{
    /// <summary>
    /// The logic to use to evaluate the conditions.
    /// Valid values are "and" and "or".
    /// </summary>
    public string? LogicalOperator { get; init; }

    /// <summary>
    /// The conditions that must be met for the rule to be considered successful.
    /// </summary>
    public IReadOnlyCollection<Condition> Conditions { get; init; }
}
```

### Condition

An abstract `Condition` that can represent either a property check or nested conditions:

```csharp
public abstract record Condition
{
    public Property? Property { get; init; }           // For property-based conditions
    public NestedConditions? Conditions { get; init; }  // For nested conditions
    public string? IntegrityHash { get; init; }        // For caching support (optional)
}
```

### Property

A `Property` defines a specific property check:

```csharp
public record Property
{
    public required string Name { get; init; }         // Property path (e.g., "Customer.Contact.Email")
    public required string Operator { get; init; }    // Comparison operator
    public required string[] Values { get; init; }    // Values to compare against
    public required string PropertyType { get; init; } // Type of the property
}
```

### NestedConditions

For nested condition logic:

```csharp
public record NestedConditions : IRule
{
    public required string? LogicalOperator { get; init; }  // "and" or "or"
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}
```

### Result

The evaluation result with detailed information:

```csharp
public record Result
{
    public bool IsMatch { get; init; }                                        // Whether conditions matched
    public required IReadOnlyCollection<ResultCondition> MatchingConditions { get; init; }    // Conditions that matched
    public required IReadOnlyCollection<ResultCondition> NonMatchingConditions { get; init; } // Conditions that didn't match
}
```

### EngineConfiguration

Optional configuration for the rules engine:

```csharp
public class EngineConfiguration
{
    public Type[]? CustomTypes { get; init; }  // Custom types for enum resolution
    public CacheConfiguration? CacheConfiguration { get; init; }  // Caching configuration
}

public record CacheConfiguration
{
    public TimeSpan Expiration { get; init; } = TimeSpan.FromMinutes(5);  // Cache expiration
}
```

## Supported Operators

The rules engine supports various operators:

- **Equality**: `Equals`, `NotEqualsTo`
- **Comparison**: `GreaterThan`, `LessThan`, `GreaterThanOrEqual`, `LessThanOrEqual`
- **String**: `Contains`, `StartsWith`, `EndsWith`, `NotStartsWith`, `NotEndsWith`
- **Range**: `Between`

## Supported Property Types

- **String** - Text values
- **Integer** - Numeric values
- **Decimal** - Decimal values
- **IpAddress** - IP address values
- **Enum** - Enumeration values (requires CustomTypes configuration)

## Usage Examples

### Example 1: Simple Property Check

```csharp
// Block payments with "failed" status
PaymentRule failedPaymentRule = new PaymentRule
{
    Id = "block-failed",
    LogicalOperator = "and",
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Status",
                Operator = "Equals",
                Values = new[] { "failed" },
                PropertyType = "String"
            }
        }
    }
};

Payment payment = new Payment
{
    Id = "pay_123",
    Status = "failed",
    Amount = 1000,
    Currency = "USD"
};

Result result = _rulesEngine.Evaluate(failedPaymentRule, payment);
// result.IsMatch will be true (rule matched - should block)
// result.MatchingConditions will contain the status condition
```

### Example 2: Multiple Conditions with AND Logic

```csharp
// Block high-value USD payments (both conditions must match)
PaymentRule highValueUsdRule = new PaymentRule
{
    Id = "high-usd-block",
    LogicalOperator = "and", // Both conditions must be true
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Amount",
                Operator = "GreaterThan",
                Values = new[] { "5000" },
                PropertyType = "Integer"
            }
        },
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Currency",
                Operator = "Equals",
                Values = new[] { "USD" },
                PropertyType = "String"
            }
        }
    }
};
```

### Example 3: Multiple Conditions with OR Logic

```csharp
// Block payments that are either high-value OR from suspicious domains
PaymentRule suspiciousPaymentRule = new PaymentRule
{
    Id = "suspicious-payment",
    LogicalOperator = "or", // Either condition can trigger the rule
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Amount",
                Operator = "GreaterThan",
                Values = new[] { "10000" },
                PropertyType = "Integer"
            }
        },
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Customer.Contact.Email",
                Operator = "EndsWith",
                Values = new[] { "@suspicious.com" },
                PropertyType = "String"
            }
        }
    }
};
```

### Example 4: Nested Conditions

```csharp
// Complex rule with nested conditions
PaymentRule complexRule = new PaymentRule
{
    Id = "complex-rule",
    LogicalOperator = "and",
    Conditions = new[]
    {
        // Simple condition
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Currency",
                Operator = "Equals",
                Values = new[] { "USD" },
                PropertyType = "String"
            }
        },
        // Nested conditions (high amount OR card payment)
        new PaymentCondition
        {
            Conditions = new NestedConditions
            {
                LogicalOperator = "or",
                Conditions = new[]
                {
                    new PaymentCondition
                    {
                        Property = new Property
                        {
                            Property = "Amount",
                            Operator = "GreaterThan",
                            Values = new[] { "5000" },
                            PropertyType = "Integer"
                        }
                    },
                    new PaymentCondition
                    {
                        Property = new Property
                        {
                            Property = "Method.Type",
                            Operator = "Equals",
                            Values = new[] { "MethodType.Card" },
                            PropertyType = "Enum"
                        }
                    }
                }
            }
        }
    }
};
```

### Example 5: Enum Property with Custom Types

```csharp
// Configure custom types for enum resolution
services.AddRulesEngine(new EngineConfiguration
{
    CustomTypes = [typeof(MethodType)]
});

// Rule using enum property
PaymentRule cardPaymentRule = new PaymentRule
{
    Id = "card-only",
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Method.Type",
                Operator = "Equals",
                Values = new[] { "MethodType.Card" },
                PropertyType = "Enum"
            }
        }
    }
};
```

### Example 6: Range Conditions

```csharp
// Allow payments within a specific amount range
PaymentRule rangeRule = new PaymentRule
{
    Id = "amount-range",
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Property = "Amount",
                Operator = "Between",
                Values = new[] { "100", "5000" }, // Between 100 and 5000
                PropertyType = "Integer"
            }
        }
    }
};
```

## Performance Optimization with Caching

The rules engine supports intelligent caching to improve performance when evaluating the same conditions repeatedly. Caching is **only applied when both conditions are met**:

1. **CacheConfiguration is provided** during service registration
2. **IntegrityHash is set** on the Condition class

### Enabling Caching

```csharp
// Configure caching during service registration
services.AddRulesEngine(new EngineConfiguration
{
    CacheConfiguration = new CacheConfiguration
    {
        Expiration = TimeSpan.FromMinutes(10) // Sliding expiration (default: 5 minutes)
    }
});
```

### Using IntegrityHash for Cacheable Conditions

```csharp
// Condition WITH caching (IntegrityHash provided)
PaymentCondition cachedCondition = new PaymentCondition
{
    IntegrityHash = "payment-amount-gt-1000", // Unique identifier for this condition
    Property = new Property
    {
        Property = "Amount",
        Operator = "GreaterThan",
        Values = new[] { "1000" },
        PropertyType = "Integer"
    }
};

// Condition WITHOUT caching (IntegrityHash is null)
PaymentCondition nonCachedCondition = new PaymentCondition
{
    // IntegrityHash = null (default) - no caching
    Property = new Property
    {
        Property = "Amount",
        Operator = "GreaterThan",
        Values = new[] { "1000" },
        PropertyType = "Integer"
    }
};
```

### Cache Behavior

- **Cache Key**: Uses the `IntegrityHash` value as the cache key
- **Cache Type**: Sliding expiration - cache entry is renewed on each access
- **Cache Storage**: In-memory using `Microsoft.Extensions.Caching.Memory`
- **Thread Safety**: Cache operations are thread-safe
- **Performance**: Cached conditions skip expression compilation and use pre-compiled functions

### Best Practices for IntegrityHash

```csharp
// ✅ Good: Descriptive and unique hash
IntegrityHash = "payment-amount-between-100-5000"

// ✅ Good: Include key condition details
IntegrityHash = "customer-email-ends-suspicious-com"

// ✅ Good: Use consistent naming convention
IntegrityHash = "rule-high-value-usd-payments"

// ❌ Avoid: Generic or non-descriptive hashes
IntegrityHash = "condition1"

// ❌ Avoid: Same hash for different conditions
IntegrityHash = "amount-check" // Used for both GreaterThan and LessThan
```

### Performance Impact

When caching is enabled with proper `IntegrityHash` values:
- **First evaluation**: Normal performance (expression compilation + execution)
- **Subsequent evaluations**: ~10-100x faster (cached pre-compiled function)
- **Memory usage**: Minimal overhead for cached expressions
- **Cache expiration**: Automatic cleanup based on sliding expiration

## API Reference

### IRulesEngine<T>

The main interface for rule evaluation:

```csharp
public interface IRulesEngine<in T>
{
    /// <summary>
    /// Evaluate a rule against a fact.
    /// </summary>
    /// <param name="rule">The rule to evaluate.</param>
    /// <param name="fact">The fact to evaluate against.</param>
    /// <returns>A Result object with evaluation details.</returns>
    public Result Evaluate(IRule rule, T fact);
}
```

### ServiceCollectionExtensions

Extension methods for dependency injection:

```csharp
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(
        this IServiceCollection services,
        EngineConfiguration? configuration = null
    );
}
```

## Advanced Usage

### Rule Evaluation Service with Detailed Results

```csharp
public class PaymentRuleService
{
    private readonly IRulesEngine<Payment> _rulesEngine;
    private readonly List<IRule> _rules;

    public PaymentRuleService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
        _rules = LoadRulesFromDatabase(); // Your rule storage
    }

    public PaymentValidationResult ValidatePayment(Payment payment)
    {
        PaymentValidationResult result = new PaymentValidationResult { IsValid = true };

        foreach (IRule rule in _rules)
        {
            Result evaluationResult = _rulesEngine.Evaluate(rule, payment);

            RuleEvaluationResult ruleResult = new RuleEvaluationResult
            {
                RuleId = GetRuleId(rule), // Your method to get rule ID
                IsMatch = evaluationResult.IsMatch,
                MatchingConditions = evaluationResult.MatchingConditions.ToList(),
                NonMatchingConditions = evaluationResult.NonMatchingConditions.ToList()
            };

            result.RuleResults.Add(ruleResult);

            if (evaluationResult.IsMatch)
            {
                // Handle rule match based on your business logic
                result.IsValid = false; // For blocking rules
                result.TriggeredRules.Add(GetRuleId(rule));
            }
        }

        return result;
    }

    private string GetRuleId(IRule rule)
    {
        // Implementation depends on your rule structure
        return rule switch
        {
            PaymentRule paymentRule => paymentRule.Id,
            _ => "unknown"
        };
    }
}

public class PaymentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> TriggeredRules { get; set; } = new();
    public List<RuleEvaluationResult> RuleResults { get; set; } = new();
}

public class RuleEvaluationResult
{
    public required string RuleId { get; set; }
    public bool IsMatch { get; set; }
    public List<ResultCondition> MatchingConditions { get; set; } = new();
    public List<ResultCondition> NonMatchingConditions { get; set; } = new();
}
```

### Optimizing Performance with Caching

Here's how to configure rules with caching for better performance:

```csharp
public class CachedPaymentRuleService
{
    private readonly IRulesEngine<Payment> _rulesEngine;
    private readonly List<IRule> _rules;

    public CachedPaymentRuleService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
        _rules = LoadOptimizedRules(); // Rules with IntegrityHash for caching
    }

    private List<IRule> LoadOptimizedRules()
    {
        return new List<PaymentRule>
        {
            new PaymentRule
            {
                Id = "high-value-usd-block",
                LogicalOperator = "and",
                Conditions = new[]
                {
                    new PaymentCondition
                    {
                        IntegrityHash = "amount-gt-5000", // Cached condition
                        Property = new Property
                        {
                            Property = "Amount",
                            Operator = "GreaterThan",
                            Values = new[] { "5000" },
                            PropertyType = "Integer"
                        }
                    },
                    new PaymentCondition
                    {
                        IntegrityHash = "currency-usd", // Cached condition
                        Property = new Property
                        {
                            Property = "Currency",
                            Operator = "Equals",
                            Values = new[] { "USD" },
                            PropertyType = "String"
                        }
                    }
                }
            }
        };
    }

    public PaymentValidationResult ValidatePayment(Payment payment)
    {
        // This will benefit from cached expressions on subsequent calls
        PaymentValidationResult result = new PaymentValidationResult { IsValid = true };

        foreach (IRule rule in _rules)
        {
            Result evaluationResult = _rulesEngine.Evaluate(rule, payment);

            if (evaluationResult.IsMatch)
            {
                result.IsValid = false;
                result.TriggeredRules.Add(rule.Id ?? "unknown");
            }
        }

        return result;
    }
}
```

### Testing Rules

```csharp
[Test]
public void RulesEngine_Should_Block_High_Value_Payments()
{
    // Arrange
    ServiceCollection services = new ServiceCollection();
    services.AddRulesEngine();

    ServiceProvider provider = services.BuildServiceProvider();
    IRulesEngine<Payment> rulesEngine = provider.GetRequiredService<IRulesEngine<Payment>>();

    PaymentRule rule = new PaymentRule
    {
        Id = "test-rule",
        LogicalOperator = "and",
        Conditions = new[]
        {
            new PaymentCondition
            {
                Property = new Property
                {
                    Property = "Amount",
                    Operator = "GreaterThan",
                    Values = new[] { "1000" },
                    PropertyType = "Integer"
                }
            }
        }
    };

    Payment payment = new Payment
    {
        Id = "test",
        Status = "pending",
        Amount = 1500,
        Currency = "USD"
    };

    // Act
    Result result = rulesEngine.Evaluate(rule, payment);

    // Assert
    Assert.That(result.IsMatch, Is.True);
    Assert.That(result.MatchingConditions, Has.Count.EqualTo(1));
    Assert.That(result.NonMatchingConditions, Is.Empty);
}

[Test]
public void RulesEngine_Should_Handle_OR_Logic()
{
    // Arrange
    ServiceCollection services = new ServiceCollection();
    services.AddRulesEngine();

    ServiceProvider provider = services.BuildServiceProvider();
    IRulesEngine<Payment> rulesEngine = provider.GetRequiredService<IRulesEngine<Payment>>();

    PaymentRule rule = new PaymentRule
    {
        Id = "or-rule",
        LogicalOperator = "or", // Either condition can match
        Conditions = new[]
        {
            new PaymentCondition
            {
                Property = new Property
                {
                    Property = "Amount",
                    Operator = "GreaterThan",
                    Values = new[] { "10000" },
                    PropertyType = "Integer"
                }
            },
            new PaymentCondition
            {
                Property = new Property
                {
                    Property = "Status",
                    Operator = "Equals",
                    Values = new[] { "failed" },
                    PropertyType = "String"
                }
            }
        }
    };

    Payment payment = new Payment
    {
        Id = "test",
        Status = "failed", // This condition matches
        Amount = 500,      // This condition doesn't match
        Currency = "USD"
    };

    // Act
    Result result = rulesEngine.Evaluate(rule, payment);

    // Assert
    Assert.That(result.IsMatch, Is.True); // Should match because of OR logic
    Assert.That(result.MatchingConditions, Has.Count.EqualTo(1));
    Assert.That(result.NonMatchingConditions, Has.Count.EqualTo(1));
}
```

## Requirements

- **.NET 8.0** or later
- **Microsoft.Extensions.DependencyInjection.Abstractions** 9.0.8 or later
- **Microsoft.Extensions.Caching.Memory** 9.0.8 or later
- **System.Linq.Dynamic.Core** 1.6.7 or later
- **FastExpressionCompiler** 5.3.0 or later

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Development Setup

1. Clone the repository
2. Ensure you have .NET 8.0 SDK installed
3. Run `dotnet restore` to restore dependencies
4. Run `dotnet build` to build the solution
5. Run `dotnet test` to execute tests

### Code Style

This project uses:
- **C# 12** language features
- **Nullable reference types** enabled
- **ImplicitUsings** enabled
- **CSharpier** for code formatting

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or contributions, please visit the [GitHub repository](https://github.com/paytently/core-rules-engine).
