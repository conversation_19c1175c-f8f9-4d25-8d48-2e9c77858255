# Paytently Core Rules Engine

A flexible and extensible rules engine for .NET applications that allows you to define and evaluate business rules against any type of data object.

## Overview

The Paytently Core Rules Engine provides a simple yet powerful way to define business rules and evaluate them against your domain objects. It supports conditional logic with various operators and can be used for scenarios like payment validation, fraud detection, access control, and more.

## Installation

### .NET CLI
```bash
# For contracts only
dotnet add package Paytently.Core.Rules.Engine.Contracts

# For full implementation
dotnet add package Paytently.Core.Rules.Engine
```

## Quick Start

### 1. Register the Rules Engine

Add the rules engine to your dependency injection container:

```csharp
using Paytently.Core.Rules.Engine;

// In your Program.cs or Startup.cs
services.AddRulesEngine();
```

### 2. Define Your Data Model

```csharp
public record Payment
{
    public required string Id { get; init; }
    public required string Status { get; init; }
    public int Amount { get; init; }
    public required string Currency { get; init; }
    public Customer? Customer { get; init; }
}

public record Customer
{
    public required string FirstName { get; init; }
    public required string LastName { get; init; }
    public required Contact Contact { get; init; }
}

public record Contact
{
    public required string Email { get; init; }
}
```

### 3. Create and Evaluate Rules

```csharp
using Paytently.Core.Rules.Engine.Contracts;

public class PaymentService
{
    private readonly IRulesEngine<Payment> _rulesEngine;

    public PaymentService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
    }

    public bool ValidatePayment(Payment payment)
    {
        // Define a rule to block high-value payments
        var highValueRule = new Rule
        {
            Id = "high-value-block",
            Name = "Block High Value Payments",
            Type = Type.Block,
            Conditions = new[]
            {
                new Condition
                {
                    Property = "Amount",
                    Operator = "GreaterThan",
                    Values = new[] { "10000" }
                }
            }
        };

        // Evaluate the rule
        bool shouldBlock = _rulesEngine.Evaluate(highValueRule, payment);
        return !shouldBlock; // Return true if payment is valid (not blocked)
    }
}
```

## Core Concepts

### Rule

A `Rule` represents a business rule that can be evaluated against a data object.

```csharp
public record Rule
{
    public required string Id { get; init; }        // Unique identifier
    public required string Name { get; init; }      // Human-readable name
    public required Type Type { get; init; }        // Block or Allow
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}
```

### Type

Defines the rule type:

```csharp
public enum Type
{
    Block,  // Rule blocks/prevents action when conditions are met
    Allow   // Rule allows action when conditions are met
}
```

### Condition

A `Condition` defines a specific check within a rule:

```csharp
public record Condition
{
    public required string[] Values { get; init; }    // Values to compare against
    public required string Operator { get; init; }   // Comparison operator
    public required string Property { get; init; }   // Property path to evaluate
}
```

## Usage Examples

### Example 1: Simple Property Check

```csharp
// Block payments with "failed" status
var failedPaymentRule = new Rule
{
    Id = "block-failed",
    Name = "Block Failed Payments",
    Type = Type.Block,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Status",
            Operator = "Equals",
            Values = new[] { "failed" }
        }
    }
};

var payment = new Payment
{
    Id = "pay_123",
    Status = "failed",
    Amount = 1000,
    Currency = "USD"
};

bool isBlocked = _rulesEngine.Evaluate(failedPaymentRule, payment);
// isBlocked will be true
```

### Example 2: Multiple Conditions

```csharp
// Block high-value USD payments
var highValueUsdRule = new Rule
{
    Id = "high-usd-block",
    Name = "Block High Value USD Payments",
    Type = Type.Block,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Amount",
            Operator = "GreaterThan",
            Values = new[] { "5000" }
        },
        new Condition
        {
            Property = "Currency",
            Operator = "Equals",
            Values = new[] { "USD" }
        }
    }
};
```

### Example 3: Nested Property Access

```csharp
// Block payments from specific email domains
var emailDomainRule = new Rule
{
    Id = "block-domain",
    Name = "Block Suspicious Email Domains",
    Type = Type.Block,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Customer.Contact.Email",
            Operator = "EndsWith",
            Values = new[] { "@suspicious.com", "@blocked.net" }
        }
    }
};
```

### Example 4: Allow Rules

```csharp
// Allow payments only from verified customers
var verifiedCustomerRule = new Rule
{
    Id = "allow-verified",
    Name = "Allow Verified Customers",
    Type = Type.Allow,
    Conditions = new[]
    {
        new Condition
        {
            Property = "Customer.IsVerified",
            Operator = "Equals",
            Values = new[] { "true" }
        }
    }
};
```

## Supported Operators

The rules engine supports various operators for different comparison types:

- **Equality**: `Equals`, `NotEquals`
- **Comparison**: `GreaterThan`, `LessThan`, `GreaterThanOrEqual`, `LessThanOrEqual`
- **String**: `Contains`, `StartsWith`, `EndsWith`
- **Collection**: `In`, `NotIn`
- **Existence**: `IsNull`, `IsNotNull`

*Note: The actual implementation of operators depends on the rules engine implementation.*

## API Reference

### IRulesEngine<T>

The main interface for rule evaluation:

```csharp
public interface IRulesEngine<in T>
{
    public bool Evaluate(Rule rule, T fact);
}
```

**Parameters:**
- `rule`: The rule to evaluate
- `fact`: The data object to evaluate against

**Returns:** `true` if the rule conditions are met, `false` otherwise

### ServiceCollectionExtensions

Extension methods for dependency injection:

```csharp
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(this IServiceCollection services);
}
```

## Advanced Usage

### Custom Rule Evaluation Service

```csharp
public class PaymentRuleService
{
    private readonly IRulesEngine<Payment> _rulesEngine;
    private readonly List<Rule> _rules;

    public PaymentRuleService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
        _rules = LoadRules();
    }

    public PaymentValidationResult ValidatePayment(Payment payment)
    {
        var result = new PaymentValidationResult { IsValid = true };

        foreach (var rule in _rules)
        {
            bool ruleMatches = _rulesEngine.Evaluate(rule, payment);
            
            if (ruleMatches)
            {
                if (rule.Type == Type.Block)
                {
                    result.IsValid = false;
                    result.BlockedByRules.Add(rule.Id);
                }
                else if (rule.Type == Type.Allow)
                {
                    result.AllowedByRules.Add(rule.Id);
                }
            }
        }

        return result;
    }
}

public class PaymentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> BlockedByRules { get; set; } = new();
    public List<string> AllowedByRules { get; set; } = new();
}
```

### Testing Rules

```csharp
[Test]
public void RulesEngine_Should_Block_High_Value_Payments()
{
    // Arrange
    var rulesEngine = new RulesEngine<Payment>();
    var rule = new Rule
    {
        Id = "test-rule",
        Name = "Test High Value Block",
        Type = Type.Block,
        Conditions = new[]
        {
            new Condition
            {
                Property = "Amount",
                Operator = "GreaterThan",
                Values = new[] { "1000" }
            }
        }
    };
    
    var payment = new Payment
    {
        Id = "test",
        Status = "pending",
        Amount = 1500,
        Currency = "USD"
    };

    // Act
    bool shouldBlock = rulesEngine.Evaluate(rule, payment);

    // Assert
    Assert.That(shouldBlock, Is.True);
}
```

### Development Setup

1. Clone the repository
2. Ensure you have .NET 8.0 SDK installed
3. Run `dotnet restore` to restore dependencies
4. Run `dotnet build` to build the solution
5. Run `dotnet test` to execute tests

