# Paytently Core Rules Engine

A flexible and extensible rules engine for .NET applications that allows you to define and evaluate business rules against any type of data object.

## Overview

The Paytently Core Rules Engine provides a simple yet powerful way to define business rules and evaluate them against your domain objects. It supports conditional logic with various operators, nested conditions, and can be used for scenarios like payment validation, fraud detection, access control, and more.

## Features

- 🎯 **Type-safe rule evaluation** - Generic interface supports any data type
- 🔧 **Flexible condition system** - Support for multiple operators and property paths
- 🚀 **Easy dependency injection** - Built-in service collection extensions
- 📦 **Two NuGet packages** - Contracts for interfaces, Engine for implementation
- ⚡ **High-performance evaluation** - Uses FastExpressionCompiler for optimized rule execution
- 🔌 **Nested conditions** - Support for complex logical structures with AND/OR operations
- 📊 **Detailed results** - Returns matching and non-matching conditions for analysis
- 🏷️ **Custom type support** - Built-in support for enums and custom types

## Installation

Install the NuGet packages using your preferred method:

### Package Manager Console
```powershell
# For contracts only (if you only need interfaces)
Install-Package Paytently.Core.Rules.Engine.Contracts

# For full implementation
Install-Package Paytently.Core.Rules.Engine
```

### .NET CLI
```bash
# For contracts only
dotnet add package Paytently.Core.Rules.Engine.Contracts

# For full implementation
dotnet add package Paytently.Core.Rules.Engine
```

### PackageReference
```xml
<PackageReference Include="Paytently.Core.Rules.Engine.Contracts" Version="*" />
<PackageReference Include="Paytently.Core.Rules.Engine" Version="*" />
```

## Quick Start

### 1. Register the Rules Engine

Add the rules engine to your dependency injection container:

```csharp
using Paytently.Core.Rules.Engine;

// In your Program.cs or Startup.cs
services.AddRulesEngine();
```

You can also specify custom types for enum resolution if needed:

```csharp
services.AddRulesEngine(new EngineConfiguration
{
    CustomTypes = [typeof(PaymentStatus), typeof(MethodType)]
});
```

### 2. Define Your Data Model

```csharp
public record Payment
{
    public required string Id { get; init; }
    public required string Status { get; init; }
    public int Amount { get; init; }
    public required string Currency { get; init; }
    public Customer? Customer { get; init; }
    public Method? Method { get; init; }
}

public record Customer
{
    public required string FirstName { get; init; }
    public required string LastName { get; init; }
    public required Contact Contact { get; init; }
}

public record Contact
{
    public required string Email { get; init; }
}

public record Method
{
    public required MethodType Type { get; init; }
}

public enum MethodType
{
    Card,
    OpenBanking
}
```

### 3. Create and Evaluate Rules

```csharp
using Paytently.Core.Rules.Engine.Contracts;

public class PaymentService
{
    private readonly IRulesEngine<Payment> _rulesEngine;

    public PaymentService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
    }

    public bool ValidatePayment(Payment payment)
    {
        // Define a rule to block high-value payments
        PaymentRule highValueRule = new PaymentRule
        {
            Id = "high-value-block",
            Conditions = new[]
            {
                new PaymentCondition
                {
                    Property = new Property
                    {
                        Name = "Amount",
                        Operator = "GreaterThan",
                        Values = new[] { "10000" },
                        PropertyType = "Integer"
                    }
                }
            }
        };

        // Evaluate the rule
        Result result = _rulesEngine.Evaluate(highValueRule, payment);
        
        // result.IsMatch = true means the rule conditions matched
        return !result.IsMatch; // Return true if payment is valid (not blocked)
    }
}

// Implementation of IRule for your domain
public record PaymentRule : IRule
{
    public string? Equatability { get; init; } = "and"; // Default to AND logic
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}

// Implementation of Condition for simple property checks
public record PaymentCondition : Condition
{
    // Property is inherited from base Condition class
    // Conditions is inherited for nested conditions (can be null for simple conditions)
}
```

## Core Concepts

### IRule

The `IRule` interface represents a business rule that can be evaluated against a data object:

```csharp
public interface IRule
{
    /// <summary>
    /// The logic to use to evaluate the conditions.
    /// Valid values are "and" and "or".
    /// </summary>
    public string? Equatability { get; init; }

    /// <summary>
    /// The conditions that must be met for the rule to be considered successful.
    /// </summary>
    public IReadOnlyCollection<Condition> Conditions { get; init; }
}
```

### Condition

An abstract `Condition` that can represent either a property check or nested conditions:

```csharp
public abstract record Condition
{
    public Property? Property { get; init; }           // For property-based conditions
    public EquatableConditions? Conditions { get; init; }  // For nested conditions
}
```

### Property

A `Property` defines a specific property check:

```csharp
public record Property
{
    public required string Name { get; init; }         // Property path (e.g., "Customer.Contact.Email")
    public required string Operator { get; init; }    // Comparison operator
    public required string[] Values { get; init; }    // Values to compare against
    public required string PropertyType { get; init; } // Type of the property
}
```

### EquatableConditions

For nested condition logic:

```csharp
public record EquatableConditions : IRule
{
    public required string? Equatability { get; init; }  // "and" or "or"
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}
```

### Result

The evaluation result with detailed information:

```csharp
public record Result
{
    public bool IsMatch { get; init; }                                        // Whether conditions matched
    public required IReadOnlyCollection<ResultCondition> MatchingConditions { get; init; }    // Conditions that matched
    public required IReadOnlyCollection<ResultCondition> NonMatchingConditions { get; init; } // Conditions that didn't match
}
```

### EngineConfiguration

Optional configuration for the rules engine:

```csharp
public class EngineConfiguration
{
    public Type[]? CustomTypes { get; init; }  // Custom types for enum resolution
}
```

## Supported Operators

The rules engine supports various operators:

- **Equality**: `Equals`, `NotEqualsTo`
- **Comparison**: `GreaterThan`, `LessThan`, `GreaterThanOrEqual`, `LessThanOrEqual`
- **String**: `Contains`, `StartsWith`, `EndsWith`, `NotStartsWith`, `NotEndsWith`
- **Range**: `Between`

## Supported Property Types

- **String** - Text values
- **Integer** - Numeric values
- **Decimal** - Decimal values
- **IpAddress** - IP address values
- **Enum** - Enumeration values (requires CustomTypes configuration)

## Usage Examples

### Example 1: Simple Property Check

```csharp
// Block payments with "failed" status
PaymentRule failedPaymentRule = new PaymentRule
{
    Id = "block-failed",
    Equatability = "and",
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Status",
                Operator = "Equals",
                Values = new[] { "failed" },
                PropertyType = "String"
            }
        }
    }
};

Payment payment = new Payment
{
    Id = "pay_123",
    Status = "failed",
    Amount = 1000,
    Currency = "USD"
};

Result result = _rulesEngine.Evaluate(failedPaymentRule, payment);
// result.IsMatch will be true (rule matched - should block)
// result.MatchingConditions will contain the status condition
```

### Example 2: Multiple Conditions with AND Logic

```csharp
// Block high-value USD payments (both conditions must match)
PaymentRule highValueUsdRule = new PaymentRule
{
    Id = "high-usd-block",
    Equatability = "and", // Both conditions must be true
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Amount",
                Operator = "GreaterThan",
                Values = new[] { "5000" },
                PropertyType = "Integer"
            }
        },
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Currency",
                Operator = "Equals",
                Values = new[] { "USD" },
                PropertyType = "String"
            }
        }
    }
};
```

### Example 3: Multiple Conditions with OR Logic

```csharp
// Block payments that are either high-value OR from suspicious domains
PaymentRule suspiciousPaymentRule = new PaymentRule
{
    Id = "suspicious-payment",
    Equatability = "or", // Either condition can trigger the rule
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Amount",
                Operator = "GreaterThan",
                Values = new[] { "10000" },
                PropertyType = "Integer"
            }
        },
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Customer.Contact.Email",
                Operator = "EndsWith",
                Values = new[] { "@suspicious.com" },
                PropertyType = "String"
            }
        }
    }
};
```

### Example 4: Nested Conditions

```csharp
// Complex rule with nested conditions
PaymentRule complexRule = new PaymentRule
{
    Id = "complex-rule",
    Equatability = "and",
    Conditions = new[]
    {
        // Simple condition
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Currency",
                Operator = "Equals",
                Values = new[] { "USD" },
                PropertyType = "String"
            }
        },
        // Nested conditions (high amount OR card payment)
        new PaymentCondition
        {
            Conditions = new EquatableConditions
            {
                Equatability = "or",
                Conditions = new[]
                {
                    new PaymentCondition
                    {
                        Property = new Property
                        {
                            Name = "Amount",
                            Operator = "GreaterThan",
                            Values = new[] { "5000" },
                            PropertyType = "Integer"
                        }
                    },
                    new PaymentCondition
                    {
                        Property = new Property
                        {
                            Name = "Method.Type",
                            Operator = "Equals",
                            Values = new[] { "MethodType.Card" },
                            PropertyType = "Enum"
                        }
                    }
                }
            }
        }
    }
};
```

### Example 5: Enum Property with Custom Types

```csharp
// Configure custom types for enum resolution
services.AddRulesEngine(new EngineConfiguration
{
    CustomTypes = [typeof(MethodType)]
});

// Rule using enum property
PaymentRule cardPaymentRule = new PaymentRule
{
    Id = "card-only",
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Method.Type",
                Operator = "Equals",
                Values = new[] { "MethodType.Card" },
                PropertyType = "Enum"
            }
        }
    }
};
```

### Example 6: Range Conditions

```csharp
// Allow payments within a specific amount range
PaymentRule rangeRule = new PaymentRule
{
    Id = "amount-range",
    Conditions = new[]
    {
        new PaymentCondition
        {
            Property = new Property
            {
                Name = "Amount",
                Operator = "Between",
                Values = new[] { "100", "5000" }, // Between 100 and 5000
                PropertyType = "Integer"
            }
        }
    }
};
```

## API Reference

### IRulesEngine<T>

The main interface for rule evaluation:

```csharp
public interface IRulesEngine<in T>
{
    /// <summary>
    /// Evaluate a rule against a fact.
    /// </summary>
    /// <param name="rule">The rule to evaluate.</param>
    /// <param name="fact">The fact to evaluate against.</param>
    /// <returns>A Result object with evaluation details.</returns>
    public Result Evaluate(IRule rule, T fact);
}
```

### ServiceCollectionExtensions

Extension methods for dependency injection:

```csharp
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(
        this IServiceCollection services,
        EngineConfiguration? configuration = null
    );
}
```

## Advanced Usage

### Rule Evaluation Service with Detailed Results

```csharp
public class PaymentRuleService
{
    private readonly IRulesEngine<Payment> _rulesEngine;
    private readonly List<IRule> _rules;

    public PaymentRuleService(IRulesEngine<Payment> rulesEngine)
    {
        _rulesEngine = rulesEngine;
        _rules = LoadRulesFromDatabase(); // Your rule storage
    }

    public PaymentValidationResult ValidatePayment(Payment payment)
    {
        PaymentValidationResult result = new PaymentValidationResult { IsValid = true };

        foreach (IRule rule in _rules)
        {
            Result evaluationResult = _rulesEngine.Evaluate(rule, payment);

            RuleEvaluationResult ruleResult = new RuleEvaluationResult
            {
                RuleId = GetRuleId(rule), // Your method to get rule ID
                IsMatch = evaluationResult.IsMatch,
                MatchingConditions = evaluationResult.MatchingConditions.ToList(),
                NonMatchingConditions = evaluationResult.NonMatchingConditions.ToList()
            };

            result.RuleResults.Add(ruleResult);

            if (evaluationResult.IsMatch)
            {
                // Handle rule match based on your business logic
                result.IsValid = false; // For blocking rules
                result.TriggeredRules.Add(GetRuleId(rule));
            }
        }

        return result;
    }

    private string GetRuleId(IRule rule)
    {
        // Implementation depends on your rule structure
        return rule switch
        {
            PaymentRule paymentRule => paymentRule.Id,
            _ => "unknown"
        };
    }
}

public class PaymentValidationResult
{
    public bool IsValid { get; set; }
    public List<string> TriggeredRules { get; set; } = new();
    public List<RuleEvaluationResult> RuleResults { get; set; } = new();
}

public class RuleEvaluationResult
{
    public required string RuleId { get; set; }
    public bool IsMatch { get; set; }
    public List<ResultCondition> MatchingConditions { get; set; } = new();
    public List<ResultCondition> NonMatchingConditions { get; set; } = new();
}
```

### Testing Rules

```csharp
[Test]
public void RulesEngine_Should_Block_High_Value_Payments()
{
    // Arrange
    ServiceCollection services = new ServiceCollection();
    services.AddRulesEngine();

    ServiceProvider provider = services.BuildServiceProvider();
    IRulesEngine<Payment> rulesEngine = provider.GetRequiredService<IRulesEngine<Payment>>();

    PaymentRule rule = new PaymentRule
    {
        Id = "test-rule",
        Equatability = "and",
        Conditions = new[]
        {
            new PaymentCondition
            {
                Property = new Property
                {
                    Name = "Amount",
                    Operator = "GreaterThan",
                    Values = new[] { "1000" },
                    PropertyType = "Integer"
                }
            }
        }
    };

    Payment payment = new Payment
    {
        Id = "test",
        Status = "pending",
        Amount = 1500,
        Currency = "USD"
    };

    // Act
    Result result = rulesEngine.Evaluate(rule, payment);

    // Assert
    Assert.That(result.IsMatch, Is.True);
    Assert.That(result.MatchingConditions, Has.Count.EqualTo(1));
    Assert.That(result.NonMatchingConditions, Is.Empty);
}

[Test]
public void RulesEngine_Should_Handle_OR_Logic()
{
    // Arrange
    ServiceCollection services = new ServiceCollection();
    services.AddRulesEngine();

    ServiceProvider provider = services.BuildServiceProvider();
    IRulesEngine<Payment> rulesEngine = provider.GetRequiredService<IRulesEngine<Payment>>();

    PaymentRule rule = new PaymentRule
    {
        Id = "or-rule",
        Equatability = "or", // Either condition can match
        Conditions = new[]
        {
            new PaymentCondition
            {
                Property = new Property
                {
                    Name = "Amount",
                    Operator = "GreaterThan",
                    Values = new[] { "10000" },
                    PropertyType = "Integer"
                }
            },
            new PaymentCondition
            {
                Property = new Property
                {
                    Name = "Status",
                    Operator = "Equals",
                    Values = new[] { "failed" },
                    PropertyType = "String"
                }
            }
        }
    };

    Payment payment = new Payment
    {
        Id = "test",
        Status = "failed", // This condition matches
        Amount = 500,      // This condition doesn't match
        Currency = "USD"
    };

    // Act
    Result result = rulesEngine.Evaluate(rule, payment);

    // Assert
    Assert.That(result.IsMatch, Is.True); // Should match because of OR logic
    Assert.That(result.MatchingConditions, Has.Count.EqualTo(1));
    Assert.That(result.NonMatchingConditions, Has.Count.EqualTo(1));
}
```

## Requirements

- **.NET 8.0** or later
- **Microsoft.Extensions.DependencyInjection.Abstractions** 9.0.8 or later
- **System.Linq.Dynamic.Core** 1.6.7 or later
- **FastExpressionCompiler** 5.3.0 or later

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Development Setup

1. Clone the repository
2. Ensure you have .NET 8.0 SDK installed
3. Run `dotnet restore` to restore dependencies
4. Run `dotnet build` to build the solution
5. Run `dotnet test` to execute tests

### Code Style

This project uses:
- **C# 12** language features
- **Nullable reference types** enabled
- **ImplicitUsings** enabled
- **CSharpier** for code formatting

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or contributions, please visit the [GitHub repository](https://github.com/paytently/core-rules-engine).
