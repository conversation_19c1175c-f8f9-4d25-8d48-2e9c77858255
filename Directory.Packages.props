<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup Label="Core">
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="[9.0.6,)" />
    <PackageVersion Include="Roslyn.Diagnostics.Analyzers" Version="[3.11.0-beta1.24165.2,)">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="Serilog" Version="4.2.0" />
  </ItemGroup>
  <ItemGroup Label="Testing">
    <PackageVersion Include="Bogus" Version="35.6.2" />
    <PackageVersion Include="AwesomeAssertions" Version="8.0.2" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="NUnit" Version="4.3.2" />
    <PackageVersion Include="NUnit3TestAdapter" Version="5.0.0" />
    <PackageVersion Include="NUnit.Analyzers" Version="4.9.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
  </ItemGroup>
</Project>