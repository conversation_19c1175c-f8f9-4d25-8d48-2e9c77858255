<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup Label="Core">
    <PackageVersion Include="BenchmarkDotNet" Version="0.15.2" />
    <PackageVersion Include="FastExpressionCompiler" Version="[5.3.0,)" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="[9.0.9,)" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="[9.0.9,)" />
    <PackageVersion Include="Roslyn.Diagnostics.Analyzers" Version="[3.11.0-beta1.24165.2,)">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="System.Linq.Dynamic.Core" Version="1.6.7" />
  </ItemGroup>
  <ItemGroup Label="Testing">
    <PackageVersion Include="Bogus" Version="35.6.2" />
    <PackageVersion Include="AwesomeAssertions" Version="8.2.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.9" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="NUnit" Version="4.4.0" />
    <PackageVersion Include="NUnit3TestAdapter" Version="5.1.0" />
    <PackageVersion Include="NUnit.Analyzers" Version="4.10.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
  </ItemGroup>
</Project>