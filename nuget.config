<?xml version="1.0" encoding="utf-8"?>

<configuration>
  <packageSources>
    <clear />
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="paytently" value="https://nuget.pkg.github.com/paytently/index.json" />
  </packageSources>
  <packageSourceCredentials>
    <paytently>
      <add key="Username" value="chawin-tree-paytently" />
      <add key="ClearTextPassword" value="%PTLY_ARTIFACTS_API_KEY%" />
    </paytently>
  </packageSourceCredentials>
  <packageSourceMapping>
    <!-- key value for <packageSource> should match key values from <packageSources> element -->
    <packageSource key="nuget.org">
      <package pattern="*" />
    </packageSource>
    <packageSource key="paytently">
      <package pattern="Paytently.*" />
    </packageSource>
  </packageSourceMapping>
</configuration>
