namespace Paytently.Core.Rules.Engine;

internal static class Constants
{
    internal static class Operator
    {
        internal const string EqualsTo = "Equals";
        internal const string NotEqualsTo = "NotEqualsTo";
        internal const string Contains = "Contains";
        internal const string Between = "Between";
        internal const string GreaterThan = "GreaterThan";
        internal const string LowerThan = "LowerThan";
        internal const string LessThan = "LessThan";
        internal const string GreaterThanOrEqual = "GreaterThanOrEqual";
        internal const string LowerThanOrEqual = "LowerThanOrEqual";
        internal const string LessThanOrEqual = "LessThanOrEqual";
        internal const string StartsWith = "StartsWith";
        internal const string EndsWith = "EndsWith";
        internal const string NotEndsWith = "NotEndsWith";
        internal const string NotStartsWith = "NotStartsWith";
    }

    internal static class PropertyTypes
    {
        internal const string String = "String";
        internal const string Integer = "Integer";
        internal const string Decimal = "Decimal";
        internal const string IpAddress = "IpAddress";
        internal const string Enum = "Enum";
    }

    internal static class Equatability
    {
        internal const string And = "and";
        internal const string Or = "or";
    }
}
