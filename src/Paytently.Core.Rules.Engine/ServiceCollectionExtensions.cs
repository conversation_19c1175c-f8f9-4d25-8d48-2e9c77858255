using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.DependencyInjection;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;
using Type = System.Type;

namespace Paytently.Core.Rules.Engine;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(
        this IServiceCollection services,
        EngineConfiguration configuration
    )
    {
        if(!ImplementsConditionTranslator(configuration.ConditionTranslatorType, out Type? genericArgument))
        {
            throw new ArgumentException("ConditionTranslatorType must implement IConditionTranslator");
        }
        

        ServiceDescriptor serviceDescriptor =
            new(
                typeof(IConditionTranslator<>).MakeGenericType(genericArgument),
                configuration.ConditionTranslatorType,
                ServiceLifetime.Singleton);
        services.Add(serviceDescriptor);
        services.AddSingleton(typeof(EvaluatorFunctionBuilder<>), typeof(EvaluatorFunctionBuilder<>));
        services.AddSingleton(typeof(IRulesEngine<>), typeof(RulesEngine<>));
        return services;
    }
    
    static bool ImplementsConditionTranslator(Type type, [NotNullWhen(true)] out Type? genericArgument)
    {
        Type openGenericInterfaceType = typeof(IConditionTranslator<>);
        if (!openGenericInterfaceType.IsGenericTypeDefinition)
        {
            throw new ArgumentException("The provided interface type must be an open generic type definition (e.g., typeof(IMyInterface<>)).", nameof(openGenericInterfaceType));
        }

        // Ge t all interfaces implemented by the type
        Type[] interfaces = type.GetInterfaces();

        foreach (Type implementedInterface in interfaces)
        {
            // Check if the implemented interface is a generic type
            if (implementedInterface.IsGenericType)
            {
                // Get the generic type definition of the implemented interface
                Type genericTypeDefinition = implementedInterface.GetGenericTypeDefinition();

                // Compare it with the open generic interface type
                if (genericTypeDefinition == openGenericInterfaceType)
                {
                    genericArgument = implementedInterface.GetGenericArguments()[0];
                    return true;
                }
            }
        }
        genericArgument = null;
        return false;
    }
}
