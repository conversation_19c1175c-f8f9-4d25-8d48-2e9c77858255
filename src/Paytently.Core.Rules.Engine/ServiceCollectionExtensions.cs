using Microsoft.Extensions.DependencyInjection;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;

namespace Paytently.Core.Rules.Engine;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(
        this IServiceCollection services,
        EngineConfiguration? configuration = null
    )
    {
        services.AddSingleton(typeof(EvaluatorFunctionBuilder<>));
        if (configuration?.CustomTypes is not null && configuration.CustomTypes.Length > 0)
        {
            services.AddSingleton(new CustomTypeProvider(configuration.CustomTypes));
        }
        else
        {
            services.AddSingleton(CustomTypeProvider.Empty);
        }
        services.AddSingleton(typeof(IRulesEngine<>), typeof(RulesEngine<>));
        return services;
    }
}
