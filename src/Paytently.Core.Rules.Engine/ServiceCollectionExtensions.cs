using Microsoft.Extensions.DependencyInjection;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;

namespace Paytently.Core.Rules.Engine;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(this IServiceCollection services)
    {
        services.AddSingleton(typeof(IRulesEngine<>), typeof(RulesEngine<>));
        return services;
    }
}
