using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;

namespace Paytently.Core.Rules.Engine;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRulesEngine(
        this IServiceCollection services,
        EngineConfiguration? configuration = null
    )
    {
        services.AddSingleton(new MemoryCache(new MemoryCacheOptions()));
        services.AddSingleton<IMemoryCache>(sp => sp.GetRequiredService<MemoryCache>());
        services.AddSingleton(typeof(EvaluatorFunctionBuilder<>));
        configuration ??= new EngineConfiguration();
        services.AddSingleton(configuration);
        if (configuration.CustomTypes is not null && configuration.CustomTypes.Length > 0)
        {
            services.AddSingleton(new CustomTypeProvider(configuration.CustomTypes));
        }
        else
        {
            services.AddSingleton(CustomTypeProvider.Empty);
        }
        services.AddSingleton(typeof(IRulesEngine<>), typeof(RulesEngine<>));
        return services;
    }
}
