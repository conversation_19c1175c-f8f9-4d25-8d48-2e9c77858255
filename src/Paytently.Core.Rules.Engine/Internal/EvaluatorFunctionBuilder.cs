using System.Data;
using System.Linq.Dynamic.Core;
using System.Linq.Dynamic.Core.CustomTypeProviders;
using System.Linq.Expressions;
using FastExpressionCompiler;
using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Internal;

internal sealed class EvaluatorFunctionBuilder<T>
{
    private readonly IDynamicLinqCustomTypeProvider? _customTypeProvider;

    public EvaluatorFunctionBuilder(CustomTypeProvider customTypeProvider)
    {
        HashSet<Type>? customTypes = customTypeProvider.GetCustomTypes();
        if (customTypes is not null && customTypes.Count > 0)
        {
            _customTypeProvider = new DynamicLinqCustomTypeProvider(customTypes);
        }
    }

    internal Func<T, bool> Build(Condition condition)
    {
        if (condition.Property is null)
        {
            return _ => false;
        }

        Expression expression = ConditionTranslator.Translate(condition.Property);

        Expression<Func<T, bool>>? lambda;
        try
        {
            lambda = (Expression<Func<T, bool>>?)
                DynamicExpressionParser.ParseLambda(
                    new ParsingConfig()
                    {
                        CustomTypeProvider = _customTypeProvider,
                        AllowNewToEvaluateAnyType = true,
                        ResolveTypesBySimpleName = true
                    },
                    [System.Linq.Expressions.Expression.Parameter(typeof(T), "x")],
                    typeof(bool),
                    expression.Value,
                    expression.PropertyName
                );
        }
        catch (Exception e)
        {
            throw new InvalidExpressionException("Failed to parse expression", e);
        }

        return lambda.CompileFast();
    }
}
