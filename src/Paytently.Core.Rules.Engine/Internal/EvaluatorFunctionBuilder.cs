using System.Data;
using System.Linq.Dynamic.Core;
using System.Linq.Dynamic.Core.CustomTypeProviders;
using System.Linq.Expressions;
using FastExpressionCompiler;
using Microsoft.Extensions.Caching.Memory;
using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Internal;

internal sealed class EvaluatorFunctionBuilder<T>
{
    private readonly IDynamicLinqCustomTypeProvider? _customTypeProvider;
    private readonly MemoryCache? _cache;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(5);

    public EvaluatorFunctionBuilder(
        CustomTypeProvider customTypeProvider,
        EngineConfiguration configuration,
        MemoryCache cache
    )
    {
        if (configuration.CacheConfiguration?.Expiration is not null)
        {
            _cacheExpiration = configuration.CacheConfiguration.Expiration;
            _cache = cache;
        }

        HashSet<Type>? customTypes = customTypeProvider.GetCustomTypes();
        if (customTypes is not null && customTypes.Count > 0)
        {
            _customTypeProvider = new DynamicLinqCustomTypeProvider(customTypes);
        }
    }

    internal Func<T, bool> Build(Condition condition)
    {
        if (_cache is not null && !string.IsNullOrEmpty(condition.IntegrityHash))
        {
            return _cache.GetOrCreate(
                    condition.IntegrityHash,
                    entry =>
                    {
                        entry.SetSlidingExpiration(_cacheExpiration);
                        return CompileCondition(condition);
                    }
                ) ?? CompileCondition(condition);
        }

        return CompileCondition(condition);
    }

    private Func<T, bool> CompileCondition(Condition condition)
    {
        if (condition.Property is null)
        {
            return _ => false;
        }

        Expression expression = ConditionTranslator.Translate(condition.Property);

        Expression<Func<T, bool>>? lambda;
        try
        {
            lambda = (Expression<Func<T, bool>>?)
                DynamicExpressionParser.ParseLambda(
                    new ParsingConfig()
                    {
                        CustomTypeProvider = _customTypeProvider,
                        AllowNewToEvaluateAnyType = true,
                        ResolveTypesBySimpleName = true
                    },
                    [System.Linq.Expressions.Expression.Parameter(typeof(T), "x")],
                    typeof(bool),
                    expression.Value,
                    expression.PropertyName
                );
        }
        catch (Exception e)
        {
            throw new InvalidExpressionException("Failed to parse expression", e);
        }

        return lambda.CompileFast();
    }
}
