using System.Data;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using FastExpressionCompiler;
using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Internal;

internal sealed class EvaluatorFunctionBuilder<T>
{
    private readonly IConditionTranslator<T> _conditionTranslator;

    public EvaluatorFunctionBuilder(IConditionTranslator<T> conditionTranslator) =>
        _conditionTranslator = conditionTranslator;

    internal Func<T, bool> Build(Condition condition)
    {
        string expression = _conditionTranslator.Translate(condition);
        Expression<Func<T, bool>>? lambda;
        try
        {
            lambda = DynamicExpressionParser.ParseLambda<T, bool>(
                ParsingConfig.Default,
                true,
                expression
            );
        }
        catch (Exception e)
        {
            throw new InvalidExpressionException("Failed to parse expression", e);
        }

        return lambda.CompileFast();
    }
}
