using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Exceptions;

namespace Paytently.Core.Rules.Engine.Internal;

internal record Expression
{
    public string? PropertyName { get; init; }
    public required string Value { get; init; }
}

internal static class ConditionTranslator
{
    internal static Expression Translate(Property condition)
    {
        string property = condition.Name;
        string @operator = condition.Operator;
        string[] values = condition.Values;
        string propertyType = condition.PropertyType;

        ValidateCondition(propertyType, @operator, values);

        string propertyName;
        if (property == "[self]")
        {
            propertyName = "it";
        }
        else
        {
            propertyName = property.Contains(".")
                ? $"np(x.{property}) != null && np(x.{property})"
                : $"x.{property}";
        }

        string value = values[0];

        value = propertyType switch
        {
            Constants.PropertyTypes.String => $"\"{value}\"",
            Constants.PropertyTypes.IpAddress => $"\"{value}\"",
            _ => value
        };

        string expression = @operator switch
        {
            Constants.Operator.EqualsTo => $"{propertyName} == {value}",
            Constants.Operator.NotEqualsTo => $"{propertyName} != {value}",
            Constants.Operator.Between
                => $"{propertyName} >= {value} && {propertyName} <= {values[1]}",
            Constants.Operator.GreaterThan => $"{propertyName} > {value}",
            Constants.Operator.LowerThan => $"{propertyName} < {value}",
            Constants.Operator.LessThan => $"{propertyName} < {value}",
            Constants.Operator.GreaterThanOrEqual => $"{propertyName} >= {value}",
            Constants.Operator.LowerThanOrEqual => $"{propertyName} <= {value}",
            Constants.Operator.LessThanOrEqual => $"{propertyName} <= {value}",
            Constants.Operator.Contains => $"{propertyName}.Contains({value})",
            Constants.Operator.StartsWith => $"{propertyName}.StartsWith({value})",
            Constants.Operator.EndsWith => $"{propertyName}.EndsWith({value})",
            _ => throw new ArgumentOutOfRangeException(nameof(@operator), @operator, null)
        };

        return new() { PropertyName = propertyName == "it" ? "it" : "x", Value = expression };
    }

    private static void ValidateCondition(string propertyType, string @operator, string[] values)
    {
        if (
            propertyType == Constants.PropertyTypes.Enum
            && @operator != Constants.Operator.EqualsTo
            && @operator != Constants.Operator.NotEqualsTo
        )
        {
            throw new InvalidConditionException("Enum property type only supports Equals operator");
        }

        if (@operator == Constants.Operator.Between)
        {
            if (values.Length != 2)
            {
                throw new InvalidConditionException("Between operator requires two values");
            }

            if (propertyType == Constants.PropertyTypes.String)
            {
                throw new InvalidConditionException(
                    "Between operator does not support string values"
                );
            }
        }
        else if (values.Length != 1)
        {
            throw new InvalidConditionException($"{nameof(@operator)} operator requires one value");
        }

        if (
            @operator
                is Constants.Operator.Contains
                    or Constants.Operator.StartsWith
                    or Constants.Operator.EndsWith
            && propertyType != Constants.PropertyTypes.String
        )
        {
            throw new InvalidConditionException("Contains operator requires a string value");
        }
    }
}
