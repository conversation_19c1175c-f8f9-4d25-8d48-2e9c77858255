using Paytently.Core.Rules.Engine;
using Paytently.Core.Rules.Engine.Extensions;

namespace Paytently.Core.Rules.Engine.Internal;

internal sealed class RulesEngine<T> : IRulesEngine<T>
{
    private readonly EvaluatorFunctionBuilder<T> _builder;

    // ReSharper disable once StaticMemberInGenericType
    private static readonly Result NoMatchingConditions =
        new()
        {
            IsMatch = false,
            MatchingConditions = [],
            NonMatchingConditions = []
        };

    public RulesEngine(EvaluatorFunctionBuilder<T> builder) => _builder = builder;

    public Result Evaluate(IRule rule, T fact) =>
        rule.Equatability switch
        {
            Constants.Equatability.And => EvaluateAnd(rule, fact),
            Constants.Equatability.Or => EvaluateOr(rule, fact),
            null => EvaluateAnd(rule, fact),
            _ => throw new ArgumentException("Invalid equatability value")
        };

    private Result EvaluateOr(IRule rule, T fact)
    {
        List<ResultCondition> matchingConditions = [];
        List<ResultCondition> nonMatchingConditions = [];
        int matchingCount = EvaluateAllConditions(
            fact,
            rule.Conditions,
            matchingConditions,
            nonMatchingConditions
        );

        return new()
        {
            IsMatch = matchingCount > 0,
            MatchingConditions = matchingConditions,
            NonMatchingConditions = nonMatchingConditions
        };
    }

    private Result EvaluateAnd(IRule rule, T fact)
    {
        List<ResultCondition> matchingConditions = [];
        List<ResultCondition> nonMatchingConditions = [];
        int matchingCount = EvaluateAllConditions(
            fact,
            rule.Conditions,
            matchingConditions,
            nonMatchingConditions
        );

        return new()
        {
            IsMatch = matchingCount == rule.Conditions.Count,
            MatchingConditions = matchingConditions,
            NonMatchingConditions = nonMatchingConditions
        };
    }

    private int EvaluateAllConditions(
        T fact,
        IReadOnlyCollection<Condition> conditions,
        List<ResultCondition> matchingConditions,
        List<ResultCondition> nonMatchingConditions
    )
    {
        int matchingCount = 0;
        foreach (Condition condition in conditions)
        {
            matchingCount += condition.Property is null
                ? EvaluateAsEquatableConditionsAndReturnMatchCount(
                    fact,
                    condition,
                    matchingConditions,
                    nonMatchingConditions
                )
                : EvaluateAsPropertyAndReturnMatchCount(
                    fact,
                    condition,
                    matchingConditions,
                    nonMatchingConditions
                );
        }

        return matchingCount;
    }

    private int EvaluateAsEquatableConditionsAndReturnMatchCount(
        T fact,
        Condition condition,
        List<ResultCondition> matchingConditions,
        List<ResultCondition> nonMatchingConditions
    )
    {
        Result equatableConditionsMatch = EquatableConditionsMatch(condition, fact);
        if (equatableConditionsMatch.IsMatch)
        {
            matchingConditions.AddRange(equatableConditionsMatch.MatchingConditions);
        }
        else
        {
            nonMatchingConditions.AddRange(equatableConditionsMatch.NonMatchingConditions);
        }

        return equatableConditionsMatch.IsMatch ? 1 : 0;
    }

    private Result EquatableConditionsMatch(Condition condition, T fact) =>
        condition.Conditions is null ? NoMatchingConditions : Evaluate(condition.Conditions, fact);

    private int EvaluateAsPropertyAndReturnMatchCount(
        T fact,
        Condition condition,
        List<ResultCondition> matchingConditions,
        List<ResultCondition> nonMatchingConditions
    )
    {
        List<Condition> conditions = Flatten(condition);
        foreach (Condition flattenedCondition in conditions)
        {
            Func<T, bool> expression = _builder.Build(flattenedCondition);
            bool match = expression.Invoke(fact);
            if (match)
            {
                matchingConditions.Add(flattenedCondition.ToResultCondition());
                return 1;
            }
        }

        nonMatchingConditions.Add(condition.ToResultCondition());
        return 0;
    }

    private static List<Condition> Flatten(Condition condition) =>
        condition.Property switch
        {
            null => [condition],
            _
                => condition.Property.Operator switch
                {
                    Constants.Operator.Between => [condition],
                    Constants.Operator.GreaterThan => [condition],
                    Constants.Operator.LowerThan => [condition],
                    Constants.Operator.LessThan => [condition],
                    Constants.Operator.GreaterThanOrEqual => [condition],
                    Constants.Operator.LowerThanOrEqual => [condition],
                    Constants.Operator.LessThanOrEqual => [condition],
                    _
                        => condition
                            .Property.Values.Select(c =>
                                condition with
                                {
                                    Property = condition.Property with { Values = [c] }
                                }
                            )
                            .ToList()
                }
        };
}
