using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Internal;

internal sealed class RulesEngine<T> : IRulesEngine<T>
{
    private static readonly Result Success = new() { Success = true };

    private readonly EvaluatorFunctionBuilder<T> _evaluatorFunctionBuilder;

    public RulesEngine(EvaluatorFunctionBuilder<T> evaluatorFunctionBuilder) =>
        _evaluatorFunctionBuilder = evaluatorFunctionBuilder;

    public Result Evaluate(Rule rule, T fact)
    {
        List<Condition> matchingConditions = [];
        List<Condition> nonMatchingConditions = [];
        foreach (Condition condition in rule.Conditions)
        {
            Func<T, bool> expression = _evaluatorFunctionBuilder.Build(condition);
            bool isTrue = expression(fact);
            if (isTrue)
            {
                matchingConditions.Add(condition);
            }
            else
            {
                nonMatchingConditions.Add(condition);
            }
        }

        return nonMatchingConditions.Count == 0
            ? Success
            : new()
            {
                Success = false,
                MatchingConditions = matchingConditions,
                NonMatchingConditions = nonMatchingConditions
            };
    }
}
