using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;

namespace Paytently.Core.Rules.Engine.Internal;

internal sealed class RulesEngine<T> : IRulesEngine<T>
{
    private readonly EvaluatorFunctionBuilder<T> _builder;

    // ReSharper disable once StaticMemberInGenericType
    private static readonly Result NoMatchingConditions =
        new()
        {
            IsMatch = false,
            MatchingConditions = [],
            NonMatchingConditions = []
        };

    public RulesEngine(EvaluatorFunctionBuilder<T> builder) => _builder = builder;

    public Result Evaluate(IRule rule, T fact) =>
        rule.LogicalOperator switch
        {
            Constants.LogicalOperator.And => EvaluateAnd(rule.Conditions, fact),
            Constants.LogicalOperator.Or => EvaluateOr(rule.Conditions, fact),
            null => EvaluateAnd(rule.Conditions, fact),
            _ => throw new ArgumentException("Invalid LogicalOperator value")
        };

    private Result EvaluateOr(IReadOnlyCollection<Condition>? conditions, T fact)
    {
        if (conditions is null)
        {
            return NoMatchingConditions;
        }

        List<ResultCondition> matchingConditions = [];
        List<ResultCondition> nonMatchingConditions = [];
        int matchingCount = MatchAnyConditions(
            fact,
            conditions,
            matchingConditions,
            nonMatchingConditions
        );

        return new()
        {
            IsMatch = matchingCount > 0,
            MatchingConditions = matchingConditions,
            NonMatchingConditions = nonMatchingConditions
        };
    }

    private Result EvaluateAnd(IReadOnlyCollection<Condition>? conditions, T fact)
    {
        if (conditions is null)
        {
            return NoMatchingConditions;
        }

        List<ResultCondition> matchingConditions = [];
        List<ResultCondition> nonMatchingConditions = [];
        int matchingCount = MatchAllConditions(
            fact,
            conditions,
            matchingConditions,
            nonMatchingConditions
        );

        return new()
        {
            IsMatch = matchingCount == conditions.Count,
            MatchingConditions = matchingConditions,
            NonMatchingConditions = nonMatchingConditions
        };
    }

    private int MatchAnyConditions(
        T fact,
        IReadOnlyCollection<Condition> conditions,
        List<ResultCondition> matchingConditions,
        List<ResultCondition> nonMatchingConditions
    )
    {
        int matchingCount = 0;
        foreach (Condition condition in conditions)
        {
            matchingCount += condition.Property is null
                ? EvaluateAsNestedConditionsAndReturnMatchCount(
                    fact,
                    condition,
                    matchingConditions,
                    nonMatchingConditions
                )
                : EvaluateAsPropertyAndReturnMatchCount(
                    fact,
                    condition,
                    matchingConditions,
                    nonMatchingConditions
                );

            if (matchingCount == 1)
            {
                break;
            }
        }

        return matchingCount;
    }

    private int MatchAllConditions(
        T fact,
        IReadOnlyCollection<Condition> conditions,
        List<ResultCondition> matchingConditions,
        List<ResultCondition> nonMatchingConditions
    )
    {
        int matchingCount = 0;
        foreach (Condition condition in conditions)
        {
            matchingCount += condition.Property is null
                ? EvaluateAsNestedConditionsAndReturnMatchCount(
                    fact,
                    condition,
                    matchingConditions,
                    nonMatchingConditions
                )
                : EvaluateAsPropertyAndReturnMatchCount(
                    fact,
                    condition,
                    matchingConditions,
                    nonMatchingConditions
                );
        }

        return matchingCount;
    }

    private int EvaluateAsNestedConditionsAndReturnMatchCount(
        T fact,
        Condition condition,
        List<ResultCondition> matchingConditions,
        List<ResultCondition> nonMatchingConditions
    )
    {
        Result NestedConditionsMatch = ChildConditionsMatch(condition, fact);
        if (NestedConditionsMatch.IsMatch)
        {
            matchingConditions.AddRange(NestedConditionsMatch.MatchingConditions);
        }
        else
        {
            nonMatchingConditions.AddRange(NestedConditionsMatch.NonMatchingConditions);
        }

        return NestedConditionsMatch.IsMatch ? 1 : 0;
    }

    private Result ChildConditionsMatch(Condition condition, T fact)
    {
        if (condition.Conditions is null)
        {
            return NoMatchingConditions;
        }

        return condition.LogicalOperator switch
        {
            Constants.LogicalOperator.And => EvaluateAnd(condition.Conditions, fact),
            Constants.LogicalOperator.Or => EvaluateOr(condition.Conditions, fact),
            null => EvaluateAnd(condition.Conditions, fact),
            _ => throw new ArgumentException("Invalid LogicalOperator value")
        };
    }

    private int EvaluateAsPropertyAndReturnMatchCount(
        T fact,
        Condition condition,
        List<ResultCondition> matchingConditions,
        List<ResultCondition> nonMatchingConditions
    )
    {
        List<Condition> conditions = Flatten(condition);
        foreach (Condition flattenedCondition in conditions)
        {
            Func<T, bool> expression = _builder.Build(flattenedCondition);
            bool match = expression.Invoke(fact);
            if (match)
            {
                matchingConditions.AddRange(flattenedCondition.ToResultCondition());
                return 1;
            }
        }

        nonMatchingConditions.AddRange(condition.ToResultCondition());
        return 0;
    }

    private static List<Condition> Flatten(Condition condition) =>
        condition.Property switch
        {
            null => [condition],
            _
                => condition.Operator switch
                {
                    Constants.Operator.Between => [condition],
                    Constants.Operator.GreaterThan => [condition],
                    Constants.Operator.LowerThan => [condition],
                    Constants.Operator.LessThan => [condition],
                    Constants.Operator.GreaterThanOrEqual => [condition],
                    Constants.Operator.LowerThanOrEqual => [condition],
                    Constants.Operator.LessThanOrEqual => [condition],
                    _
                        => condition.Values?.Select(c => condition with { Values = [c] }).ToList()
                            ?? []
                }
        };
}
