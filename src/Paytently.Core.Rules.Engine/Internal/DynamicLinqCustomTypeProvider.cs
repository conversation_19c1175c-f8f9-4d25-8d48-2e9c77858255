using System.Linq.Dynamic.Core.CustomTypeProviders;
using System.Reflection;

namespace Paytently.Core.Rules.Engine.Internal;

internal class DynamicLinqCustomTypeProvider : IDynamicLinqCustomTypeProvider
{
    private readonly HashSet<Type> _customTypes;

    internal DynamicLinqCustomTypeProvider(HashSet<Type> customTypes) => _customTypes = customTypes;

    public HashSet<Type> GetCustomTypes() => _customTypes;

    public Dictionary<Type, List<MethodInfo>> GetExtensionMethods() => new();

    public Type? ResolveType(string typeName) => Type.GetType(typeName);

    public Type? ResolveTypeBySimpleName(string simpleTypeName) => Type.GetType(simpleTypeName);
}
