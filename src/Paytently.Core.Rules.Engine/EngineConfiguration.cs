namespace Paytently.Core.Rules.Engine;

/// <summary>
/// A configuration class for the rules engine.
/// </summary>
public class EngineConfiguration
{
    /// <summary>
    /// Gets or sets the custom types to be used in rules evaluation.
    /// </summary>
    public Type[]? CustomTypes { get; init; }

    public CacheConfiguration? CacheConfiguration { get; init; }
}

public record CacheConfiguration
{
    public TimeSpan Expiration { get; init; } = TimeSpan.FromMinutes(5);
}
