using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Extensions;

internal static class ResultConditionExtensions
{
    internal static List<ResultCondition> ToResultCondition(this Condition condition)
    {
        List<ResultCondition> result = [];
        if (condition.Conditions is not null)
        {
            result.AddRange(condition.Conditions.SelectMany(c => c.ToResultCondition()));
            return result;
        }

        _ = condition.Property ?? throw new ArgumentNullException(nameof(condition.Property));
        _ = condition.Operator ?? throw new ArgumentNullException(nameof(condition.Operator));
        _ = condition.Values ?? throw new ArgumentNullException(nameof(condition.Values));
        _ =
            condition.PropertyType
            ?? throw new ArgumentNullException(nameof(condition.PropertyType));

        string propertyValue =
            condition.Operator == Constants.Operator.Between
                ? $"[{condition.Values[0]} - {condition.Values[1]}]"
                : condition.Values[0];

        return
        [
            new()
            {
                Property = condition.Property,
                Operator = condition.Operator,
                Value = propertyValue
            }
        ];
    }
}
