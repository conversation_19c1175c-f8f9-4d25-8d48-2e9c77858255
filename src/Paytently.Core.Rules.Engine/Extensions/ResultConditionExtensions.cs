using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Extensions;

internal static class ResultConditionExtensions
{
    internal static ResultCondition ToResultCondition(this Condition condition) =>
        new() { Property = condition.Property?.ToResultProperty(), };

    internal static ResultProperty? ToResultProperty(this Property? property)
    {
        if (property is null)
        {
            return null;
        }

        string propertyValue =
            property.Operator == Constants.Operator.Between
                ? $"[{property.Values[0]} - {property.Values[1]}]"
                : property.Values[0];

        return new()
        {
            Operator = property.Operator,
            Value = propertyValue,
            Name = property.Name,
        };
    }
}
