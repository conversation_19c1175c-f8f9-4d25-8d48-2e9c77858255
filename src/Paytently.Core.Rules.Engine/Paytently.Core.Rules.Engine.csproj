<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <PackageId>Paytently.Core.Rules.Engine</PackageId>
    <PackageProjectUrl>https://github.com/paytently/core-rules-engine</PackageProjectUrl>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>https://github.com/paytently/core-rules-engine</RepositoryUrl>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <!-- SourceLink settings -->
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <AllowedOutputExtensionsInPackageBuildOutputFolder>$(AllowedOutputExtensionsInPackageBuildOutputFolder);.pdb</AllowedOutputExtensionsInPackageBuildOutputFolder>
    <IsPackable>true</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <None Include="$(SolutionDir)README.md" Pack="true" PackagePath="\">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="..\..\README.md">
      <Link>Features\Common\README.md</Link>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FastExpressionCompiler" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
    <PackageReference Include="System.Linq.Dynamic.Core" />
  </ItemGroup>
  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Paytently.Core.Rules.Engine.Unit.Tests</_Parameter1>
    </AssemblyAttribute> <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
    <_Parameter1>Paytently.Core.Rules.Engine.Benchmarker</_Parameter1>
  </AssemblyAttribute>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Paytently.Core.Rules.Engine.Contracts\Paytently.Core.Rules.Engine.Contracts.csproj" />
  </ItemGroup>

  <Target Name="Husky" BeforeTargets="Restore;CollectPackageReferences" Condition="'$(HUSKY)' != 0">
    <Exec Command="dotnet tool restore" StandardOutputImportance="Low" StandardErrorImportance="High"/>
    <Exec Command="dotnet husky install" StandardOutputImportance="Low" StandardErrorImportance="High" WorkingDirectory="..\.."/>
  </Target>

</Project>
