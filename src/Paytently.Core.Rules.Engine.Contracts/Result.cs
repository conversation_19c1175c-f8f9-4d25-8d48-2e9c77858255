namespace Paytently.Core.Rules.Engine.Contracts;

/// <summary>
/// The result of evaluating a rule against a fact.
/// </summary>
public record Result
{
    /// <summary>
    /// Whether all conditions matched.
    /// </summary>
    public bool IsMatch { get; init; }

    /// <summary>
    /// The conditions that matched. Not empty if <see cref="IsMatch"/> is false.
    /// </summary>
    public required IReadOnlyCollection<ResultCondition> MatchingConditions { get; init; }

    /// <summary>
    /// The conditions that did not match. Not empty if <see cref="IsMatch"/> is false.
    /// </summary>
    public required IReadOnlyCollection<ResultCondition> NonMatchingConditions { get; init; }
}

public sealed record ResultCondition
{
    public ResultProperty? Property { get; init; }
}

/// <summary>
/// A property represents a property to be evaluated.
/// </summary>
public record ResultProperty
{
    /// <summary>
    /// The property path to evaluate. If the property is a nested property, use dot notation.
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// The operator to use for comparing the property value against the condition values.
    /// </summary>
    public required string Operator { get; init; }

    /// <summary>
    /// The value matched.
    /// </summary>
    public required string Value { get; init; }
}
