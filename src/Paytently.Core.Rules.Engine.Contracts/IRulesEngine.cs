namespace Paytently.Core.Rules.Engine;

/// <summary>
/// The main interface for rule evaluation.
/// </summary>
/// <typeparam name="T"></typeparam>
public interface IRulesEngine<in T>
{
    /// <summary>
    /// Evaluate a rule against a fact.
    /// </summary>
    /// <param name="rule">The rule to evaluate.</param>
    /// <param name="fact">The fact to evaluate against.</param>
    /// <returns>A <see cref="Result"/> object with evaluation details.</returns>
    public Result Evaluate(IRule rule, T fact);
}
