namespace Paytently.Core.Rules.Engine.Contracts;

public record Rule
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public required Type Type { get; init; }
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}

public enum Type
{
    Block,
    Allow
}

public enum Operator
{
    Equals,
    NotEquals,
    Contains,
    Between,
}

public record Condition
{
    public required string Property { get; init; } = "[self]";
    public required Operator Operator { get; init; }
    public required string[] Values { get; init; }
}
