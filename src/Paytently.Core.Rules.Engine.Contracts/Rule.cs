namespace Paytently.Core.Rules.Engine.Contracts;

/// <summary>
/// A rule represents a business rule that can be evaluated against a data object.
/// </summary>
public interface IRule
{
    /// <summary>
    /// The logic to use to evaluate the conditions.
    /// Valid values are "and" and "or".
    /// </summary>
    public string? Equatability { get; init; }

    /// <summary>
    /// The conditions that must be met for the rule to be considered successful.
    /// </summary>
    public IReadOnlyCollection<Condition> Conditions { get; init; }
}

/// <summary>
/// A condition defines a specific check within a rule.
/// It could either be a property or a set of conditions.
/// </summary>
public abstract record Condition
{
    public Property? Property { get; init; }

    public EquatableConditions? Conditions { get; init; }
}

/// <summary>
/// A property represents a property to be evaluated.
/// </summary>
public record Property
{
    /// <summary>
    /// The property path to evaluate. If the property is a nested property, use dot notation.
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// The operator to use for comparing the property value against the condition values.
    /// </summary>
    public required string Operator { get; init; }

    /// <summary>
    /// The values to compare against.
    /// </summary>
    public required string[] Values { get; init; }

    /// <summary>
    /// The type of the property.
    /// </summary>
    public required string PropertyType { get; init; }
}

public record EquatableConditions : IRule
{
    /// <summary>
    /// The logic to use to evaluate the conditions.
    /// Valid values are "and" and "or".
    /// </summary>
    public required string? Equatability { get; init; }

    /// <summary>
    /// The conditions to evaluate.
    /// </summary>
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}
