using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Helpers;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class ServiceCollectionExtensionsTests
{
    [Test]
    public void AddRulesEngine_RulesEngineCanBeResolved()
    {
        ServiceCollection services = new();
        services.AddRulesEngine(new EngineConfiguration
        {
            ConditionTranslatorType = typeof(PaymentConditionTranslator)
        });
        ServiceProvider provider = services.BuildServiceProvider();
        IRulesEngine<Payment>? engine = provider.GetService<IRulesEngine<Payment>>();
        engine.Should().NotBeNull();
        engine.Should().BeOfType<RulesEngine<Payment>>();
    }
}
