using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class OperatorOverrideTests
{
    private RulesEngine<MyNumber> _engine;
    private EvaluatorFunctionBuilder<MyNumber> _evaluatorFunctionBuilder;

    [SetUp]
    public void Setup()
    {
        _evaluatorFunctionBuilder = new(CustomTypeProvider.Empty);
        _engine = new RulesEngine<MyNumber>(_evaluatorFunctionBuilder);
    }

    [TestCase(
        "100",
        Constants.Operator.EqualsTo,
        true,
        TestName = "WhenEqualsConditionIsMet_ReturnsTrue"
    )]
    [TestCase(
        "200",
        Constants.Operator.EqualsTo,
        false,
        TestName = "WhenEqualsConditionIsNotMet_ReturnsFalse"
    )]
    [TestCase(
        "100",
        Constants.Operator.NotEqualsTo,
        false,
        TestName = "WhenNotEqualsConditionIsMet_ReturnsFalse"
    )]
    [TestCase(
        "200",
        Constants.Operator.NotEqualsTo,
        true,
        TestName = "WhenNotEqualsConditionIsNotMet_ReturnsTrue"
    )]
    public void EqualityTests(string fact, string op, bool success)
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = op,
                            Values = [$"{fact}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };

        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
            };
        _engine.Evaluate(rule, 100).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("500", "1500", true, TestName = "WhenBetweenConditionIsMet_ReturnsTrue")]
    [TestCase("1500", "2500", false, TestName = "WhenBetweenConditionIsNotMet_ReturnsFalse")]
    public void BetweenTests(string left, string right, bool success)
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = Constants.Operator.Between,
                            Values = [left, right],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };

        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
            };
        _engine.Evaluate(rule, 1000).Should().BeEquivalentTo(expectedResult);
    }
}
