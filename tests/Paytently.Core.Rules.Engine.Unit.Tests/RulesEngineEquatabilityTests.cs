using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineLogicalOperatorTests
{
    private RulesEngine<Payment> _engine;
    private EvaluatorFunctionBuilder<Payment> _evaluatorFunctionBuilder;
    private EngineConfiguration _configuration;
    private MemoryCache _cache;

    [TearDown]
    public void TearDown() => _cache.Dispose();

    [SetUp]
    public void Setup()
    {
        _configuration = new EngineConfiguration();
        _cache = new MemoryCache(new MemoryCacheOptions());
        _evaluatorFunctionBuilder = new(CustomTypeProvider.Empty, _configuration, _cache);
        _engine = new RulesEngine<Payment>(_evaluatorFunctionBuilder);
    }

    [Test]
    public void WithoutLogicalOperatorComparer_EvaluatesConditionsAsAndAndReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 3000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = "Amount",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["3000"],
                        PropertyType = Constants.PropertyTypes.Integer
                    },
                    new AllowBlockCondition
                    {
                        Property = "Customer.Contact.Email",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["<EMAIL>"],
                        PropertyType = Constants.PropertyTypes.String
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule
                    .Conditions.SelectMany(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        1,
        0,
        0,
        true,
        TestName = "WithLogicalOperatorComparerSetToOr_WhenBothConditionsMatch_ReturnsTrue"
    )]
    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        1,
        0,
        0,
        true,
        TestName = "WithLogicalOperatorComparerSetToOr_WhenFirstConditionMatches_ReturnsTrue"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        1,
        1,
        0,
        1,
        true,
        TestName = "WithLogicalOperatorComparerSetToOr_WhenSecondConditionMatches_ReturnsTrue"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        0,
        0,
        2,
        false,
        TestName = "WithLogicalOperatorComparerSetToOr_WhenNoConditionMatches_ReturnsFalse"
    )]
    public void WithLogicalOperatorComparerSetToOr_EvaluatesConditionsAsOrAndReturnsCorrectResult(
        int paymentAmount,
        string paymentEmail,
        int conditionAmount,
        string conditionEmail,
        int successSkip,
        int successMatch,
        int failSkip,
        int failMatch,
        bool expectedMatch
    )
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = paymentAmount,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = paymentEmail }
                }
            };
        Rule rule =
            new()
            {
                LogicalOperator = "or",
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = "Amount",
                        Operator = Constants.Operator.EqualsTo,
                        Values = [conditionAmount.ToString()],
                        PropertyType = Constants.PropertyTypes.Integer
                    },
                    new AllowBlockCondition
                    {
                        Property = "Customer.Contact.Email",
                        Operator = Constants.Operator.EqualsTo,
                        Values = [conditionEmail],
                        PropertyType = Constants.PropertyTypes.String
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = expectedMatch,
                MatchingConditions = rule
                    .Conditions.Skip(successSkip)
                    .Take(successMatch)
                    .SelectMany(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = rule
                    .Conditions.Skip(failSkip)
                    .Take(failMatch)
                    .SelectMany(c => c.ToResultCondition())
                    .ToList(),
            };
        Result result = _engine.Evaluate(rule, fact);
        result.Should().BeEquivalentTo(expectedResult);
    }

    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        2,
        0,
        0,
        true,
        TestName = "WithLogicalOperatorComparerSetToAnd_WhenBothConditionsMatch_ReturnsTrue"
    )]
    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        1,
        1,
        1,
        false,
        TestName = "WithLogicalOperatorComparerSetToAnd_WhenFirstConditionMatches_ReturnsFalse"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        1,
        1,
        0,
        1,
        false,
        TestName = "WithLogicalOperatorComparerSetToAnd_WhenSecondConditionMatches_ReturnsFalse"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        0,
        0,
        2,
        false,
        TestName = "WithLogicalOperatorComparerSetToAnd_WhenNoConditionMatches_ReturnsFalse"
    )]
    public void WithLogicalOperatorComparerSetToAnd_EvaluatesConditionsAsOrAndReturnsCorrectResult(
        int paymentAmount,
        string paymentEmail,
        int conditionAmount,
        string conditionEmail,
        int successSkip,
        int successMatch,
        int failSkip,
        int failMatch,
        bool expectedMatch
    )
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = paymentAmount,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = paymentEmail }
                }
            };
        Rule rule =
            new()
            {
                LogicalOperator = "and",
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = "Amount",
                        Operator = Constants.Operator.EqualsTo,
                        Values = [conditionAmount.ToString()],
                        PropertyType = Constants.PropertyTypes.Integer
                    },
                    new AllowBlockCondition
                    {
                        Property = "Customer.Contact.Email",
                        Operator = Constants.Operator.EqualsTo,
                        Values = [conditionEmail],
                        PropertyType = Constants.PropertyTypes.String
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = expectedMatch,
                MatchingConditions = rule
                    .Conditions.Skip(successSkip)
                    .Take(successMatch)
                    .SelectMany(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = rule
                    .Conditions.Skip(failSkip)
                    .Take(failMatch)
                    .SelectMany(c => c.ToResultCondition())
                    .ToList(),
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithInvalidLogicalOperatorComparer_ThrowsArgumentException()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                LogicalOperator = "invalid",
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = "Amount",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["1000"],
                        PropertyType = Constants.PropertyTypes.Integer
                    }
                ]
            };
        Action action = () => _engine.Evaluate(rule, fact);
        action.Should().Throw<ArgumentException>();
    }
}
