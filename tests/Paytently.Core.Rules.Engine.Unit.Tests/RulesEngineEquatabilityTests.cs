using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineEquatabilityTests
{
    private RulesEngine<Payment> _engine;
    private EvaluatorFunctionBuilder<Payment> _evaluatorFunctionBuilder;

    [SetUp]
    public void Setup()
    {
        _evaluatorFunctionBuilder = new(CustomTypeProvider.Empty);
        _engine = new RulesEngine<Payment>(_evaluatorFunctionBuilder);
    }

    [Test]
    public void WithoutEquatabilityComparer_EvaluatesConditionsAsAndAndReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 3000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["3000"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    },
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["<EMAIL>"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule.Conditions.Select(c => c.ToResultCondition()).ToList(),
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        2,
        0,
        0,
        true,
        TestName = "WithEquatabilityComparerSetToOr_WhenBothConditionsMatch_ReturnsTrue"
    )]
    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        1,
        1,
        1,
        true,
        TestName = "WithEquatabilityComparerSetToOr_WhenFirstConditionMatches_ReturnsTrue"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        1,
        1,
        0,
        1,
        true,
        TestName = "WithEquatabilityComparerSetToOr_WhenSecondConditionMatches_ReturnsTrue"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        0,
        0,
        2,
        false,
        TestName = "WithEquatabilityComparerSetToOr_WhenNoConditionMatches_ReturnsFalse"
    )]
    public void WithEquatabilityComparerSetToOr_EvaluatesConditionsAsOrAndReturnsCorrectResult(
        int paymentAmount,
        string paymentEmail,
        int conditionAmount,
        string conditionEmail,
        int successSkip,
        int successMatch,
        int failSkip,
        int failMatch,
        bool expectedMatch
    )
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = paymentAmount,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = paymentEmail }
                }
            };
        Rule rule =
            new()
            {
                Equatability = "or",
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.EqualsTo,
                            Values = [conditionAmount.ToString()],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    },
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.EqualsTo,
                            Values = [conditionEmail],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = expectedMatch,
                MatchingConditions = rule
                    .Conditions.Skip(successSkip)
                    .Take(successMatch)
                    .Select(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = rule
                    .Conditions.Skip(failSkip)
                    .Take(failMatch)
                    .Select(c => c.ToResultCondition())
                    .ToList(),
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        2,
        0,
        0,
        true,
        TestName = "WithEquatabilityComparerSetToAnd_WhenBothConditionsMatch_ReturnsTrue"
    )]
    [TestCase(
        1000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        1,
        1,
        1,
        false,
        TestName = "WithEquatabilityComparerSetToAnd_WhenFirstConditionMatches_ReturnsFalse"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        1,
        1,
        0,
        1,
        false,
        TestName = "WithEquatabilityComparerSetToAnd_WhenSecondConditionMatches_ReturnsFalse"
    )]
    [TestCase(
        2000,
        "<EMAIL>",
        1000,
        "<EMAIL>",
        0,
        0,
        0,
        2,
        false,
        TestName = "WithEquatabilityComparerSetToAnd_WhenNoConditionMatches_ReturnsFalse"
    )]
    public void WithEquatabilityComparerSetToAnd_EvaluatesConditionsAsOrAndReturnsCorrectResult(
        int paymentAmount,
        string paymentEmail,
        int conditionAmount,
        string conditionEmail,
        int successSkip,
        int successMatch,
        int failSkip,
        int failMatch,
        bool expectedMatch
    )
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = paymentAmount,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = paymentEmail }
                }
            };
        Rule rule =
            new()
            {
                Equatability = "and",
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.EqualsTo,
                            Values = [conditionAmount.ToString()],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    },
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.EqualsTo,
                            Values = [conditionEmail],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = expectedMatch,
                MatchingConditions = rule
                    .Conditions.Skip(successSkip)
                    .Take(successMatch)
                    .Select(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = rule
                    .Conditions.Skip(failSkip)
                    .Take(failMatch)
                    .Select(c => c.ToResultCondition())
                    .ToList(),
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithInvalidEquatabilityComparer_ThrowsArgumentException()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Equatability = "invalid",
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["1000"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Action action = () => _engine.Evaluate(rule, fact);
        action.Should().Throw<ArgumentException>();
    }
}
