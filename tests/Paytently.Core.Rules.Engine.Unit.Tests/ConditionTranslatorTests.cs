using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Exceptions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class ConditionTranslatorTests
{
    [Test]
    public void Translate_WhenPropertyIsSelf_ReturnsCorrectExpression()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "[self]",
                Operator = Constants.Operator.EqualsTo,
                Values = ["test"],
                PropertyType = Constants.PropertyTypes.String
            };
        Expression expected = new() { PropertyName = "it", Value = "it == \"test\"" };
        Expression expression = ConditionTranslator.Translate(property);
        expression.Should().BeEquivalentTo(expected);
    }

    [Test]
    public void Translate_WhenPropertyIsNested_ReturnsCorrectExpression()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Customer.Contact.Email",
                Operator = Constants.Operator.EqualsTo,
                Values = ["<EMAIL>"],
                PropertyType = Constants.PropertyTypes.String
            };
        Expression expected =
            new()
            {
                PropertyName = "x",
                Value =
                    "np(x.Customer.Contact.Email) != null && np(x.Customer.Contact.Email) == \"<EMAIL>\""
            };
        Expression expression = ConditionTranslator.Translate(property);
        expression.Should().BeEquivalentTo(expected);
    }

    [Test]
    public void Translate_WhenOperatorIsEquals_ReturnsCorrectExpression()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Amount",
                Operator = Constants.Operator.EqualsTo,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer
            };
        Expression expected = new() { PropertyName = "x", Value = "x.Amount == 1000" };
        Expression expression = ConditionTranslator.Translate(property);
        expression.Should().BeEquivalentTo(expected);
    }

    [Test]
    public void Translate_WhenOperatorIsNotEquals_ReturnsCorrectExpression()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Amount",
                Operator = Constants.Operator.NotEqualsTo,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer
            };
        Expression expected = new() { PropertyName = "x", Value = "x.Amount != 1000" };
        Expression expression = ConditionTranslator.Translate(property);
        expression.Should().BeEquivalentTo(expected);
    }

    [Test]
    public void Translate_WhenOperatorIsContains_ReturnsCorrectExpression()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Name",
                Operator = Constants.Operator.Contains,
                Values = ["ABC"],
                PropertyType = Constants.PropertyTypes.String
            };
        Expression expected = new() { PropertyName = "x", Value = "x.Name.Contains(\"ABC\")" };
        Expression expression = ConditionTranslator.Translate(property);
        expression.Should().BeEquivalentTo(expected);
    }

    [Test]
    public void Translate_WhenOperatorIsBetween_ReturnsCorrectExpression()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Amount",
                Operator = Constants.Operator.Between,
                Values = ["1000", "2000"],
                PropertyType = Constants.PropertyTypes.Integer
            };
        Expression expected =
            new() { PropertyName = "x", Value = "x.Amount >= 1000 && x.Amount <= 2000" };
        Expression expression = ConditionTranslator.Translate(property);
        expression.Should().BeEquivalentTo(expected);
    }

    [Test]
    public void Translate_WhenOperatorIsBetweenAndOnlyOneValueIsProvided_ThrowsException()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Amount",
                Operator = Constants.Operator.Between,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer
            };
        Action action = () => ConditionTranslator.Translate(property);
        action.Should().Throw<InvalidConditionException>();
    }

    [Test]
    public void Translate_WhenOperatorIsBetweenAndValuesAreStrings_ThrowsException()
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Amount",
                Operator = Constants.Operator.Between,
                Values = ["1000", "2000"],
                PropertyType = Constants.PropertyTypes.String
            };
        Action action = () => ConditionTranslator.Translate(property);
        action.Should().Throw<InvalidConditionException>();
    }

    [TestCase(
        Constants.Operator.Contains,
        TestName = "WhenOperatorIsContainsAndValueIsNotAString_ThrowsException"
    )]
    [TestCase(
        Constants.Operator.StartsWith,
        TestName = "WhenOperatorIsStartsWithAndValueIsNotAString_ThrowsException"
    )]
    [TestCase(
        Constants.Operator.EndsWith,
        TestName = "WhenOperatorIsEndsWithAndValueIsNotAString_ThrowsException"
    )]
    public void Translate_WhenOperatorIsStringBasedAndValueIsNotAString_ThrowsException(string op)
    {
        AllowBlockCondition property =
            new()
            {
                Property = "Amount",
                Operator = op,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer
            };
        Action action = () => ConditionTranslator.Translate(property);
        action.Should().Throw<InvalidConditionException>();
    }

    [Test]
    public void Translate_WhenPropertyTypeIsEnumAndOperatorIsNotEqualsOrNotEquals_ThrowsException() =>
        typeof(Constants.Operator)
            .GetFields()
            .Where(f => f.IsStatic && f.FieldType == typeof(string))
            .Select(f => f.GetValue(null) as string)
            .Where(op => op != Constants.Operator.EqualsTo && op != Constants.Operator.NotEqualsTo)
            .ToList()
            .ForEach(op =>
            {
                AllowBlockCondition property =
                    new()
                    {
                        Property = "Status",
                        Operator = op!,
                        Values = ["1"],
                        PropertyType = Constants.PropertyTypes.Enum
                    };
                Action action = () => ConditionTranslator.Translate(property);
                action.Should().Throw<InvalidConditionException>();
            });
}
