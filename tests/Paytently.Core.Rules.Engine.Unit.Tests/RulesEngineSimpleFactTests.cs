using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineSimpleFactTests
{
    private RulesEngine<int> _numericEngine;
    private EvaluatorFunctionBuilder<int> _numericBuilder;

    private RulesEngine<string> _stringEngine;
    private EvaluatorFunctionBuilder<string> _stringBuilder;

    private RulesEngine<MethodType> _enumEngine;
    private EvaluatorFunctionBuilder<MethodType> _enumBuilder;

    private RulesEngine<Payment> _paymentEngine;
    private EvaluatorFunctionBuilder<Payment> _paymentBuilder;

    [SetUp]
    public void Setup()
    {
        _numericBuilder = new(CustomTypeProvider.Empty);
        _numericEngine = new RulesEngine<int>(_numericBuilder);
        _stringBuilder = new(CustomTypeProvider.Empty);
        _stringEngine = new RulesEngine<string>(_stringBuilder);
        _paymentBuilder = new(CustomTypeProvider.Empty);
        _paymentEngine = new RulesEngine<Payment>(_paymentBuilder);
        _enumBuilder = new(CustomTypeProvider.Empty);
        _enumEngine = new RulesEngine<MethodType>(_enumBuilder);
    }

    [TestCase(
        "100",
        Constants.Operator.EqualsTo,
        true,
        TestName = "WhenEqualsConditionIsMet_ReturnsTrue"
    )]
    [TestCase(
        "200",
        Constants.Operator.EqualsTo,
        false,
        TestName = "WhenEqualsConditionIsNotMet_ReturnsFalse"
    )]
    [TestCase(
        "100",
        Constants.Operator.NotEqualsTo,
        false,
        TestName = "WhenNotEqualsConditionIsMet_ReturnsFalse"
    )]
    [TestCase(
        "200",
        Constants.Operator.NotEqualsTo,
        true,
        TestName = "WhenNotEqualsConditionIsNotMet_ReturnsTrue"
    )]
    public void EqualityTests(string fact, string op, bool success)
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = op,
                            Values = [$"{fact}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };

        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
            };
        _numericEngine.Evaluate(rule, 100).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("500", "1500", true, TestName = "WhenBetweenConditionIsMet_ReturnsTrue")]
    [TestCase("1500", "2500", false, TestName = "WhenBetweenConditionIsNotMet_ReturnsFalse")]
    public void BetweenTests(string left, string right, bool success)
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = Constants.Operator.Between,
                            Values = [left, right],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };

        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
            };
        _numericEngine.Evaluate(rule, 1000).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase(
        "test",
        Constants.Operator.EqualsTo,
        true,
        TestName = "WhenStringEqualsConditionIsMet_ReturnsTrue"
    )]
    [TestCase(
        "test1",
        Constants.Operator.EqualsTo,
        false,
        TestName = "WhenStringEqualsConditionIsNotMet_ReturnsFalse"
    )]
    [TestCase(
        "test",
        Constants.Operator.NotEqualsTo,
        false,
        TestName = "WhenStringNotEqualsConditionIsMet_ReturnsFalse"
    )]
    [TestCase(
        "test1",
        Constants.Operator.NotEqualsTo,
        true,
        TestName = "WhenStringNotEqualsConditionIsNotMet_ReturnsTrue"
    )]
    public void StringEqualityTests(string fact, string op, bool success)
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = op,
                            Values = [$"{fact}"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };

        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
            };
        _stringEngine.Evaluate(rule, "test").Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("t", true, TestName = "WhenBetweenConditionIsMet_ReturnsTrue")]
    [TestCase("a", false, TestName = "WhenBetweenConditionIsNotMet_ReturnsFalse")]
    public void StringContainsTests(string value, bool success)
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = Constants.Operator.Contains,
                            Values = [$"{value}"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
            };
        _stringEngine.Evaluate(rule, "test").Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void Evaluate_WithOneConditionThatContainsMultipleValuesAndConditionMatches_ReturnsResultWithFlattenedConditions()
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["test0", "test1", "test2"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions =
                [
                    new ResultCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = "Equals",
                            Value = "test1",
                        }
                    }
                ],
                NonMatchingConditions = [],
            };
        _stringEngine.Evaluate(rule, "test1").Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void Evaluate_WithMultipleConditionThatContainsMultipleValuesAllConditionsMatch_ReturnsResultWithFlattenedConditions()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 3000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["1000", "2000", "3000"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    },
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["<EMAIL>"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions =
                [
                    new ResultCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = "Equals",
                            Value = "3000",
                        }
                    },
                    new ResultCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = "Equals",
                            Value = "<EMAIL>",
                        }
                    }
                ],
                NonMatchingConditions = [],
            };
        _paymentEngine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void EvaluateMultipleConditions_WhenConditionThatContainsMultipleValuesMatchesAndOtherConditionDoesNot_ReturnsNoMatchWithCorrectConditionCollections()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 4000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["1000", "2000", "3000"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    },
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["<EMAIL>"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = false,
                MatchingConditions = [rule.Conditions.Last().ToResultCondition()],
                NonMatchingConditions = [rule.Conditions.First().ToResultCondition()]
            };
        _paymentEngine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void EvaluateMultipleConditions_WhenConditionThatContainsSingleValuesMatchesAndOtherConditionDoesNot_ReturnsNoMatchWithCorrectConditionCollections()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 3000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["1000", "2000", "3000"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    },
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["<EMAIL>"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = false,
                MatchingConditions =
                [
                    (
                        rule.Conditions.First() with
                        {
                            Property = rule.Conditions.First().Property! with { Values = ["3000"] }
                        }
                    ).ToResultCondition()
                ],
                NonMatchingConditions = [rule.Conditions.Last().ToResultCondition()]
            };
        _paymentEngine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase(
        MethodType.Card,
        Constants.Operator.EqualsTo,
        true,
        TestName = "WhenEnumEqualsConditionIsMet_ReturnsTrue"
    )]
    [TestCase(
        MethodType.OpenBanking,
        Constants.Operator.EqualsTo,
        false,
        TestName = "WhenEnumEqualsConditionIsNotMet_ReturnsFalse"
    )]
    [TestCase(
        MethodType.Card,
        Constants.Operator.NotEqualsTo,
        false,
        TestName = "WhenEnumNotEqualsConditionIsMet_ReturnsFalse"
    )]
    [TestCase(
        MethodType.OpenBanking,
        Constants.Operator.NotEqualsTo,
        true,
        TestName = "WhenEnumNotEqualsConditionIsNotMet_ReturnsTrue"
    )]
    public void EnumEqualityTests(MethodType fact, string op, bool success)
    {
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "[self]",
                            Operator = op,
                            Values = [$"{fact}"],
                            PropertyType = Constants.PropertyTypes.Enum
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
            };
        _enumEngine.Evaluate(rule, MethodType.Card).Should().BeEquivalentTo(expectedResult);
    }
}
