using Paytently.Core.Rules.Engine.Contracts;

namespace Paytently.Core.Rules.Engine.Unit.Tests.Models;

/// <summary>
/// A rule represents a business rule that can be evaluated against a data object.
/// </summary>
internal record Rule : IRule
{
    /// <summary>
    /// Unique identifier for the rule.
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// The logic to use to evaluate the conditions.
    /// </summary>
    public string? Equatability { get; init; }

    /// <summary>
    /// The conditions that must be met for the rule to be considered successful.
    /// </summary>
    public required IReadOnlyCollection<Condition> Conditions { get; init; }
}

internal record AllowBlockCondition : Condition { }
