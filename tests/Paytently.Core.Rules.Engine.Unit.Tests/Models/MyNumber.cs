namespace Paytently.Core.Rules.Engine.Unit.Tests.Models;

public class MyNumber : IComparable<MyNumber>
{
    public int Value { get; init; }

    public static implicit operator MyNumber(int value) => new() { Value = value };

    public static implicit operator int(MyNumber number) => number.Value;

    public static implicit operator string(MyNumber number) => number.Value.ToString();

    public static implicit operator MyNumber(string value) => new() { Value = int.Parse(value) };

    public int CompareTo(MyNumber? other)
    {
        if (ReferenceEquals(this, other))
            return 0;
        if (other is null)
            return 1;
        return Value.CompareTo(other.Value);
    }

    public override string ToString() => Value.ToString();

    public override bool Equals(object? obj)
    {
        if (obj is null)
            return false;
        if (obj is MyNumber other)
            return Value == other.Value;
        return false;
    }

    public override int GetHashCode() => Value.GetHashCode();

    public static bool operator ==(MyNumber? left, MyNumber? right) =>
        left?.Equals(right) ?? right is null;

    public static bool operator !=(MyNumber? left, MyNumber? right) => !(left == right);

    public static bool operator <(MyNumber left, MyNumber right) => left.CompareTo(right) < 0;

    public static bool operator >(MyNumber left, MyNumber right) => left.CompareTo(right) > 0;

    public static bool operator <=(MyNumber left, MyNumber right) => left.CompareTo(right) <= 0;

    public static bool operator >=(MyNumber left, MyNumber right) => left.CompareTo(right) >= 0;

    public static bool operator ==(MyNumber? left, int right) => left?.Value == right;

    public static bool operator !=(MyNumber? left, int right) => left?.Value != right;

    public static bool operator <(MyNumber left, int right) => left.Value < right;

    public static bool operator >(MyNumber left, int right) => left.Value > right;

    public static bool operator <=(MyNumber left, int right) => left.Value <= right;

    public static bool operator >=(MyNumber left, int right) => left.Value >= right;

    public bool Contains(string value) => Value.ToString().Contains(value);
}
