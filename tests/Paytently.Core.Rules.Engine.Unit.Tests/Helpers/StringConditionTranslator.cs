using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Exceptions;

namespace Paytently.Core.Rules.Engine.Unit.Tests.Helpers;

 internal sealed class StringConditionTranslator : IConditionTranslator<string>
{
    public string Translate(Condition condition)
    {
        if(condition.Property != "[self]")
        {
            throw new ArgumentException("Property is not supported");
        }
        
        string operation = condition.Operator switch
        {
            Operator.Equals => "Equals",
            Operator.NotEquals => "!Equals",
            Operator.Contains => "Contains",
            _ => throw new OperatorNotApplicableException()
        };
        return $"it.{operation}(\"{condition.Values[0]}\")";
    }
}
