<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <PackageReference Include="AwesomeAssertions"/>
    <PackageReference Include="Microsoft.NET.Test.Sdk"/>
    <PackageReference Include="NSubstitute"/>
    <PackageReference Include="NUnit"/>
    <PackageReference Include="NUnit3TestAdapter"/>
    <PackageReference Include="NUnit.Analyzers"/>
    <PackageReference Include="coverlet.collector"/>
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\..\src\Paytently.Core.Rules.Engine\Paytently.Core.Rules.Engine.csproj" />
  </ItemGroup>
  
</Project>
