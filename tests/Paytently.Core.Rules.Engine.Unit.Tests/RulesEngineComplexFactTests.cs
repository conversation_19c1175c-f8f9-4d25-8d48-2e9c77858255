using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineComplexFactTests
{
    private RulesEngine<Payment> _engine;
    private EvaluatorFunctionBuilder<Payment> _evaluatorFunctionBuilder;
    private EngineConfiguration _configuration;
    private MemoryCache _cache;

    [TearDown]
    public void TearDown() => _cache.Dispose();

    [SetUp]
    public void Setup()
    {
        _configuration = new EngineConfiguration();
        _cache = new MemoryCache(new MemoryCacheOptions());
        _evaluatorFunctionBuilder = new(
            new CustomTypeProvider([typeof(MethodType)]),
            _configuration,
            _cache
        );
        _engine = new RulesEngine<Payment>(_evaluatorFunctionBuilder);
    }

    [TestCase("<EMAIL>", true, TestName = "WhenContainsConditionIsMet_ReturnsTrue")]
    [TestCase(
        "<EMAIL>",
        false,
        TestName = "WhenContainsConditionIsNotMet_ReturnsFalse"
    )]
    public void ContainsTests(string email, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.Contains,
                            Values = [$"{email}"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("<EMAIL>", true, TestName = "WhenEqualsConditionIsMet_ReturnsTrue")]
    [TestCase("<EMAIL>", false, TestName = "WhenEqualsConditionIsNotMet_ReturnsFalse")]
    public void EqualsTests(string email, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.EqualsTo,
                            Values = [$"{email}"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("<EMAIL>", true, TestName = "WhenNotEqualsConditionIsMet_ReturnsTrue")]
    [TestCase(
        "<EMAIL>",
        false,
        TestName = "WhenNotEqualsConditionIsNotMet_ReturnsFalse"
    )]
    public void NotEqualsTests(string email, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = Constants.Operator.NotEqualsTo,
                            Values = [$"{email}"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("500", "1500", true, TestName = "WhenBetweenConditionIsMet_ReturnsTrue")]
    [TestCase("1500", "2500", false, TestName = "WhenBetweenConditionIsNotMet_ReturnsFalse")]
    public void BetweenTests(string left, string right, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.Between,
                            Values = [$"{left}", $"{right}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("999", true, TestName = "WhenGreaterThanConditionIsMet_ReturnsTrue")]
    [TestCase("1001", false, TestName = "WhenGreaterThanConditionIsNotMet_ReturnsFalse")]
    public void GreaterThanTests(string amount, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.GreaterThan,
                            Values = [$"{amount}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("999", true, TestName = "WhenGreaterThanOrEqualConditionIsMet_ReturnsTrue")]
    [TestCase("1001", false, TestName = "WhenGreaterThanOrEqualConditionIsNotMet_ReturnsFalse")]
    public void GreaterThanOrEqualTests(string amount, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.GreaterThanOrEqual,
                            Values = [$"{amount}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("1001", true, TestName = "WhenLessThanConditionIsMet_ReturnsTrue")]
    [TestCase("999", false, TestName = "WhenLessThanConditionIsNotMet_ReturnsFalse")]
    public void LessThanTests(string amount, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.LessThan,
                            Values = [$"{amount}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("1001", true, TestName = "WhenLessThanOrEqualConditionIsMet_ReturnsTrue")]
    [TestCase("999", false, TestName = "WhenLessThanOrEqualConditionIsNotMet_ReturnsFalse")]
    public void LessThanOrEqualTests(string amount, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.LessThanOrEqual,
                            Values = [$"{amount}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("1001", true, TestName = "WhenLowerThanConditionIsMet_ReturnsTrue")]
    [TestCase("999", false, TestName = "WhenLowerThanConditionIsNotMet_ReturnsFalse")]
    public void LowerThanTests(string amount, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.LowerThan,
                            Values = [$"{amount}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase("1001", true, TestName = "WhenLowerThanOrEqualConditionIsMet_ReturnsTrue")]
    [TestCase("999", false, TestName = "WhenLowerThanOrEqualConditionIsNotMet_ReturnsFalse")]
    public void LowerThanOrEqualTests(string amount, bool success)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Amount",
                            Operator = Constants.Operator.LowerThanOrEqual,
                            Values = [$"{amount}"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = success,
                MatchingConditions = success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : [],
                NonMatchingConditions = !success
                    ? rule.Conditions.Select(c => c.ToResultCondition()).ToList()
                    : []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [TestCase(
        Constants.Operator.Contains,
        TestName = "WhenContainsConditionIsNotMetAndPropertyIsNull_ReturnsFalse"
    )]
    [TestCase(
        Constants.Operator.EqualsTo,
        TestName = "WhenEqualsConditionIsNotMetAndPropertyIsNull_ReturnsFalse"
    )]
    [TestCase(
        Constants.Operator.NotEqualsTo,
        TestName = "WhenNotEqualsConditionIsNotMetAndPropertyIsNull_ReturnsFalse"
    )]
    public void NullPropertyTests(string op)
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD"
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Customer.Contact.Email",
                            Operator = op,
                            Values = ["email", "email1"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = false,
                MatchingConditions = [],
                NonMatchingConditions = rule.Conditions.Select(c => c.ToResultCondition()).ToList()
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WhenBetweenConditionIsNotMetAndPropertyIsNull_ReturnsFalse()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD"
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Method.Id",
                            Operator = Constants.Operator.Between,
                            Values = ["2", "3"],
                            PropertyType = Constants.PropertyTypes.Integer
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = false,
                MatchingConditions = [],
                NonMatchingConditions = rule.Conditions.Select(c => c.ToResultCondition()).ToList()
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void EnumEqualsTests_EvalutesCorrectly()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                },
                Method = new() { Type = MethodType.Card }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Method.Type",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["MethodType.Card"],
                            PropertyType = Constants.PropertyTypes.Enum
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule.Conditions.Select(c => c.ToResultCondition()).ToList(),
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void ResolveRulesEngine_WithCustomTypes_EvaluatesCorrectly()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                },
                Method = new() { Type = MethodType.Card }
            };
        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Conditions =
                [
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Method.Type",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["MethodType.Card"],
                            PropertyType = Constants.PropertyTypes.Enum
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule.Conditions.Select(c => c.ToResultCondition()).ToList(),
                NonMatchingConditions = []
            };
        ServiceCollection services = new();
        services.AddRulesEngine(new EngineConfiguration() { CustomTypes = [typeof(MethodType)] });
        ServiceProvider provider = services.BuildServiceProvider();
        IRulesEngine<Payment> engine = provider.GetRequiredService<IRulesEngine<Payment>>();
        engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }
}
