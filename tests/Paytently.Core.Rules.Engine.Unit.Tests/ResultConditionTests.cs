using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class ResultConditionTests
{
    [Test]
    public void ToResultCondition_WhenConditionIsNotBetween_ReturnsResultConditionWithSingleValue()
    {
        AllowBlockCondition condition =
            new()
            {
                Property = new()
                {
                    Name = "Amount",
                    Operator = Constants.Operator.GreaterThan,
                    Values = ["1000"],
                    PropertyType = Constants.PropertyTypes.String
                }
            };
        ResultCondition resultCondition = condition.ToResultCondition();
        resultCondition
            .Should()
            .BeEquivalentTo(
                new ResultCondition
                {
                    Property = new()
                    {
                        Name = "Amount",
                        Operator = "GreaterThan",
                        Value = "1000"
                    }
                }
            );
    }

    [Test]
    public void ToResultCondition_WhenConditionIsBetween_ReturnsResultConditionWithTwoValues()
    {
        AllowBlockCondition condition =
            new()
            {
                Property = new()
                {
                    Name = "Amount",
                    Operator = Constants.Operator.Between,
                    Values = ["1000", "2000"],
                    PropertyType = Constants.PropertyTypes.String,
                }
            };
        ResultCondition resultCondition = condition.ToResultCondition();
        resultCondition
            .Should()
            .BeEquivalentTo(
                new ResultCondition
                {
                    Property = new()
                    {
                        Name = "Amount",
                        Operator = "Between",
                        Value = "[1000 - 2000]"
                    }
                }
            );
    }
}
