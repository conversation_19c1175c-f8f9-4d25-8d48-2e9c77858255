using System.Data;
using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using NSubstitute;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class EvaluatorFunctionBuilderTests
{
    private EngineConfiguration _configuration;
    private MemoryCache _cache;

    [SetUp]
    public void Setup()
    {
        _configuration = new EngineConfiguration();
        _cache = new MemoryCache(new MemoryCacheOptions());
    }

    [TearDown]
    public void TearDown() => _cache.Dispose();

    [Test]
    public void Build_WhenExpressionIsValid_ReturnsValidFunction()
    {
        EvaluatorFunctionBuilder<string> functionBuilder =
            new(CustomTypeProvider.Empty, _configuration, _cache);
        Func<string, bool> func = functionBuilder.Build(
            new AllowBlockCondition()
            {
                Property = "[self]",
                Operator = Constants.Operator.EqualsTo,
                Values = ["test"],
                PropertyType = Constants.PropertyTypes.String
            }
        );
        func.Should().NotBeNull();
    }

    [Test]
    public void Build_WhenExpressionIsInvalid_ThrowsException()
    {
        EvaluatorFunctionBuilder<string> functionBuilder =
            new(CustomTypeProvider.Empty, _configuration, _cache);
        Action action = () =>
            functionBuilder.Build(
                new AllowBlockCondition()
                {
                    Property = "[self]",
                    Operator = Constants.Operator.EqualsTo,
                    Values = ["test"],
                    PropertyType = Constants.PropertyTypes.Integer
                }
            );
        action.Should().Throw<InvalidExpressionException>();
    }

    [Test]
    public void Build_WhenFactContainsProperties_TheReferenceToItIsNotRequired_AndFunctionExecutesSuccessfully()
    {
        EvaluatorFunctionBuilder<Payment> functionBuilder =
            new(CustomTypeProvider.Empty, _configuration, _cache);
        Payment payment =
            new()
            {
                Id = "test",
                Status = "pending",
                Amount = 1000,
                Currency = "USD"
            };
        Func<Payment, bool> func = functionBuilder.Build(
            new AllowBlockCondition
            {
                Property = "Amount",
                Operator = Constants.Operator.EqualsTo,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer
            }
        );
        func.Should().NotBeNull();
        bool result = func(payment);
        result.Should().BeTrue();
    }

    [Test]
    public void Build_WhenPropertyIsNull_ReturnsFalseThrowsException()
    {
        EvaluatorFunctionBuilder<Payment> functionBuilder =
            new(CustomTypeProvider.Empty, _configuration, _cache);

        Action act = () => functionBuilder.Build(new AllowBlockCondition { Property = null });

        act.Should().Throw<ArgumentNullException>();
    }

    [Test]
    public void Build_WhenConditionHasIntegrityHashAndCacheIsEnabled_CachesFunction()
    {
        EngineConfiguration configuration =
            new()
            {
                CacheConfiguration = new CacheConfiguration()
                {
                    Expiration = TimeSpan.FromMinutes(5)
                }
            };
        EvaluatorFunctionBuilder<Payment> functionBuilder =
            new(CustomTypeProvider.Empty, configuration, _cache);

        functionBuilder.Build(
            new AllowBlockCondition
            {
                Property = "Amount",
                Operator = Constants.Operator.EqualsTo,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer,
                IntegrityHash = "test"
            }
        );
        _cache.Get("test").Should().NotBeNull();
    }

    [Test]
    public void Build_WhenConditionHasIntegrityHashAndCacheIsDisabled_DoesNotCacheFunction()
    {
        EvaluatorFunctionBuilder<Payment> functionBuilder =
            new(CustomTypeProvider.Empty, _configuration, _cache);

        functionBuilder.Build(
            new AllowBlockCondition
            {
                Property = "Amount",
                Operator = Constants.Operator.EqualsTo,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer,
                IntegrityHash = "test"
            }
        );
        _cache.Get("test").Should().BeNull();
    }

    [Test]
    public void Build_WhenConditionHasIntegrityHashAndCacheIsEnabledAndFunctionIsCached_ReturnsCachedFunction()
    {
        EngineConfiguration configuration =
            new()
            {
                CacheConfiguration = new CacheConfiguration()
                {
                    Expiration = TimeSpan.FromMinutes(5)
                }
            };
        EvaluatorFunctionBuilder<Payment> functionBuilder =
            new(CustomTypeProvider.Empty, configuration, _cache);

        Func<Payment, bool> func1 = functionBuilder.Build(
            new AllowBlockCondition
            {
                Property = "Amount",
                Operator = Constants.Operator.EqualsTo,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer,
                IntegrityHash = "test"
            }
        );
        Func<Payment, bool> func2 = functionBuilder.Build(
            new AllowBlockCondition
            {
                Property = "Amount",
                Operator = Constants.Operator.EqualsTo,
                Values = ["1000"],
                PropertyType = Constants.PropertyTypes.Integer,
                IntegrityHash = "test"
            }
        );
        func1.Should().Be(func2);
    }
}
