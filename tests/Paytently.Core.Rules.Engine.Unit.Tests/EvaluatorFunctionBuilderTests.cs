using System.Data;
using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Helpers;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class EvaluatorFunctionBuilderTests
{
    private Condition _condition;

    [SetUp]
    public void Setup() =>
        _condition = new()
        {
            Property = "[self]",
            Operator = Operator.Equals,
            Values = ["test"]
        };

    [Test]
    public void Build_WhenExpressionIsValid_ReturnsValidFunction()
    {
        EvaluatorFunctionBuilder<string> functionBuilder = new(new StringConditionTranslator());

        Func<string, bool> func = functionBuilder.Build(_condition);
        func.Should().NotBeNull();
    }

    [Test]
    public void Build_WhenExpressionIsInvalid_ThrowsException()
    {
        EvaluatorFunctionBuilder<string> functionBuilder = new(new InvalidStringConditionTranslator());
        Action action = () => functionBuilder.Build(_condition);
        action.Should().Throw<InvalidExpressionException>();
    }
}
