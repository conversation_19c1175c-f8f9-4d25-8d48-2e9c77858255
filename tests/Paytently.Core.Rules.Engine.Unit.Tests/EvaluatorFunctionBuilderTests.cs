using System.Data;
using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class EvaluatorFunctionBuilderTests
{
    [Test]
    public void Build_WhenExpressionIsValid_ReturnsValidFunction()
    {
        EvaluatorFunctionBuilder<string> functionBuilder = new(CustomTypeProvider.Empty);
        Func<string, bool> func = functionBuilder.Build(
            new AllowBlockCondition()
            {
                Property = new()
                {
                    Name = "[self]",
                    Operator = Constants.Operator.EqualsTo,
                    Values = ["test"],
                    PropertyType = Constants.PropertyTypes.String
                }
            }
        );
        func.Should().NotBeNull();
    }

    [Test]
    public void Build_WhenExpressionIsInvalid_ThrowsException()
    {
        EvaluatorFunctionBuilder<string> functionBuilder = new(CustomTypeProvider.Empty);
        Action action = () =>
            functionBuilder.Build(
                new AllowBlockCondition()
                {
                    Property = new()
                    {
                        Name = "[self]",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["test"],
                        PropertyType = Constants.PropertyTypes.Integer
                    }
                }
            );
        action.Should().Throw<InvalidExpressionException>();
    }

    [Test]
    public void Build_WhenFactContainsProperties_TheReferenceToItIsNotRequired_AndFunctionExecutesSuccessfully()
    {
        EvaluatorFunctionBuilder<Payment> functionBuilder = new(CustomTypeProvider.Empty);
        Payment payment =
            new()
            {
                Id = "test",
                Status = "pending",
                Amount = 1000,
                Currency = "USD"
            };
        Func<Payment, bool> func = functionBuilder.Build(
            new AllowBlockCondition
            {
                Property = new()
                {
                    Name = "Amount",
                    Operator = Constants.Operator.EqualsTo,
                    Values = ["1000"],
                    PropertyType = Constants.PropertyTypes.Integer
                }
            }
        );
        func.Should().NotBeNull();
        bool result = func(payment);
        result.Should().BeTrue();
    }

    [Test]
    public void Build_WhenPropertyIsNull_ReturnsFalseFunction()
    {
        EvaluatorFunctionBuilder<Payment> functionBuilder = new(CustomTypeProvider.Empty);
        Payment payment =
            new()
            {
                Id = "test",
                Status = "pending",
                Amount = 1000,
                Currency = "USD"
            };
        Func<Payment, bool> func = functionBuilder.Build(
            new AllowBlockCondition { Property = null }
        );
        func.Should().NotBeNull();
        bool result = func(payment);
        result.Should().BeFalse();
    }
}
