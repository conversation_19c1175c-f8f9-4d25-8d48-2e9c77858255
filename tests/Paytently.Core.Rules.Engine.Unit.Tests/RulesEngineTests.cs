using System.Linq.Expressions;
using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Helpers;
using Type = Paytently.Core.Rules.Engine.Contracts.Type;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineTests
{
    private RulesEngine<string> _engine;
    private EvaluatorFunctionBuilder<string> _evaluatorFunctionBuilder;
    private IConditionTranslator<string> _conditionTranslator;


    [SetUp]
    public void Setup()
    {
        _conditionTranslator = new StringConditionTranslator();
        _evaluatorFunctionBuilder = new(_conditionTranslator);
        _engine = new RulesEngine<string>(_evaluatorFunctionBuilder);
    }

    [Test]
    public void Evaluate_Should_Return_True_When_Condition_Is_Met()
    {
        const string fact = "test";
        Rule rule = new()
        {
            Id = Guid.NewGuid().ToString(),
            Name = Guid.NewGuid().ToString(),
            Type = Type.Block,
            Conditions =
            [
                new Condition
                {
                    Property = "[self]",
                    Operator = Operator.Equals,
                    Values = [fact]
                }
            ]
        };
        _engine
            .Evaluate(rule, fact)
            .Success
            .Should()
            .BeTrue();
    }
    
    [Test]
    public void Evaluate_Should_Return_False_When_Condition_Is_Not_Met()
    {
        const string fact = "test";
        Rule rule = new()
        {
            Id = Guid.NewGuid().ToString(),
            Name = Guid.NewGuid().ToString(),
            Type = Type.Block,
            Conditions =
            [
                new Condition
                {
                    Property = "[self]",
                    Operator = Operator.Equals,
                    Values = [Guid.NewGuid().ToString()]
                }
            ]
        };
        Result result = _engine.Evaluate(rule, fact);
        result
            .Success
            .Should()
            .BeFalse();
        
        result
            .NonMatchingConditions
            .Should()
            .NotBeNull()
            .And
            .ContainSingle(c => c == rule.Conditions.First());
        
        result
            .MatchingConditions
            .Should()
            .BeEmpty();
    }
    
    [Test]
    public void Evaluate_WithMultipleConditions_WhenAllConditionsAreMet_Should_Return_True()
    {
        const string fact = "test";
        Rule rule = new()
        {
            Id = Guid.NewGuid().ToString(),
            Name = Guid.NewGuid().ToString(),
            Type = Type.Block,
            Conditions =
            [
                new Condition
                {
                    Property = "[self]",
                    Operator = Operator.Equals,
                    Values = [fact]
                },
                new Condition
                {
                    Property = "[self]",
                    Operator = Operator.Contains,
                    Values = ["t"]
                }
            ]
        };
        _engine
            .Evaluate(rule, fact)
            .Success
            .Should()
            .BeTrue();
    }
    
    [Test]
    public void Evaluate_WithMultipleConditions_WhenOneConditionIsNotMet_Should_Return_False()
    {
        const string fact = "test";
        Rule rule = new()
        {
            Id = Guid.NewGuid().ToString(),
            Name = Guid.NewGuid().ToString(),
            Type = Type.Block,
            Conditions =
            [
                new Condition
                {
                    Property = "[self]",
                    Operator = Operator.Equals,
                    Values = [fact]
                },
                new Condition
                {
                    Property = "[self]",
                    Operator = Operator.Contains,
                    Values = ["x"]
                }
            ]
        };
        Result result = _engine.Evaluate(rule, fact);
        result
            .Success
            .Should()
            .BeFalse();
        
        result
            .NonMatchingConditions
            .Should()
            .NotBeNull()
            .And
            .ContainSingle(c => c == rule.Conditions.Last());
        
        result
            .MatchingConditions
            .Should()
            .NotBeNull()
            .And
            .ContainSingle(c => c == rule.Conditions.First());
    }
}
