using FluentAssertions;
using NSubstitute;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Internal;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineTests
{
    private RulesEngine<string> _engine;
    
    [SetUp]
    public void Setup()
    {
        _engine = new RulesEngine<string>();
    }

    [Test]
    public void Evaluate_ThrowsNotImplementedException()
    {
        Action act = () => _engine.Evaluate(Arg.Any<Rule>(), "test");
        act.Should().Throw<NotImplementedException>();
    }
}
