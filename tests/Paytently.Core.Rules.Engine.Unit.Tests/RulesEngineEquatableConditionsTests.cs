using FluentAssertions;
using NUnit.Framework;
using Paytently.Core.Rules.Engine;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineEquatableConditionsTests
{
    private RulesEngine<Payment> _engine;

    [SetUp]
    public void Setup() =>
        _engine = new RulesEngine<Payment>(
            new EvaluatorFunctionBuilder<Payment>(CustomTypeProvider.Empty)
        );

    [Test]
    public void WithEquatableConditionsAndOrEquatability_EvaluatesConditionsAsConfiguredInTheEquatableConditionsAndReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Equatability = "or",
                Conditions =
                [
                    //The first condition is not met
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Currency",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["EUR"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    },
                    //The conditions within the second condition are met
                    new AllowBlockCondition
                    {
                        Conditions = new()
                        {
                            Equatability = "and",
                            Conditions =
                            [
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Amount",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["1000"],
                                        PropertyType = Constants.PropertyTypes.Integer
                                    }
                                },
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Customer.Contact.Email",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["<EMAIL>"],
                                        PropertyType = Constants.PropertyTypes.String
                                    }
                                }
                            ]
                        }
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule
                    .Conditions.Last()
                    .Conditions!.Conditions.Select(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = [rule.Conditions.First().ToResultCondition()]
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithEquatableConditionsAndAndEquatability_WhenAtLeastOneConditionIsNotMet_ReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Equatability = "and",
                Conditions =
                [
                    //The conditions within the first condition are met
                    new AllowBlockCondition
                    {
                        Conditions = new()
                        {
                            Equatability = "or",
                            Conditions =
                            [
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Amount",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["1000"],
                                        PropertyType = Constants.PropertyTypes.Integer
                                    }
                                },
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Customer.Contact.Email",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["<EMAIL>"],
                                        PropertyType = Constants.PropertyTypes.String
                                    }
                                }
                            ]
                        }
                    },
                    //The last condition is not met
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Currency",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["EUR"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    },
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = false,
                MatchingConditions = rule
                    .Conditions.First()
                    .Conditions!.Conditions.Select(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = [rule.Conditions.Last().ToResultCondition()]
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithEquatableConditionsAndAndEquatability_WhenAllConditionsAreMet_ReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Equatability = "and",
                Conditions =
                [
                    //The conditions within the first condition are met
                    new AllowBlockCondition
                    {
                        Conditions = new()
                        {
                            Equatability = "or",
                            Conditions =
                            [
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Amount",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["1000"],
                                        PropertyType = Constants.PropertyTypes.Integer
                                    }
                                },
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Customer.Contact.Email",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["<EMAIL>"],
                                        PropertyType = Constants.PropertyTypes.String
                                    }
                                }
                            ]
                        }
                    },
                    //The last condition is met
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Currency",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["USD"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    },
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions =
                [
                    rule.Conditions.Last().ToResultCondition(),
                    .. rule
                        .Conditions.First()
                        .Conditions!.Conditions.Select(c => c.ToResultCondition())
                ],
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithEquatableConditionsAndOrEquatability_WhenAllConditionsAreMet_ReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Equatability = "or",
                Conditions =
                [
                    //The conditions within the first condition are met
                    new AllowBlockCondition
                    {
                        Conditions = new()
                        {
                            Equatability = "and",
                            Conditions =
                            [
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Amount",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["1000"],
                                        PropertyType = Constants.PropertyTypes.Integer
                                    }
                                },
                                new AllowBlockCondition
                                {
                                    Property = new()
                                    {
                                        Name = "Customer.Contact.Email",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["<EMAIL>"],
                                        PropertyType = Constants.PropertyTypes.String
                                    }
                                }
                            ]
                        }
                    },
                    //The last condition is met
                    new AllowBlockCondition
                    {
                        Property = new()
                        {
                            Name = "Currency",
                            Operator = Constants.Operator.EqualsTo,
                            Values = ["USD"],
                            PropertyType = Constants.PropertyTypes.String
                        }
                    },
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions =
                [
                    rule.Conditions.Last().ToResultCondition(),
                    .. rule
                        .Conditions.First()
                        .Conditions!.Conditions.Select(c => c.ToResultCondition())
                ],
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }
}
