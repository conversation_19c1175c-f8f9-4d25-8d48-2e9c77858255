using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using NUnit.Framework;
using Paytently.Core.Rules.Engine.Contracts;
using Paytently.Core.Rules.Engine.Extensions;
using Paytently.Core.Rules.Engine.Internal;
using Paytently.Core.Rules.Engine.Unit.Tests.Models;

namespace Paytently.Core.Rules.Engine.Unit.Tests;

public class RulesEngineNestedConditionsTests
{
    private RulesEngine<Payment> _engine;
    private EngineConfiguration _configuration = new();
    private MemoryCache _cache;

    [TearDown]
    public void TearDown() => _cache.Dispose();

    [SetUp]
    public void Setup()
    {
        _configuration = new EngineConfiguration();
        _cache = new MemoryCache(new MemoryCacheOptions());
        _engine = new RulesEngine<Payment>(
            new EvaluatorFunctionBuilder<Payment>(CustomTypeProvider.Empty, _configuration, _cache)
        );
    }

    [Test]
    public void WithNestedConditionsAndOrLogicalOperator_EvaluatesConditionsAsConfiguredInTheNestedConditionsAndReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                LogicalOperator = "or",
                Conditions =
                [
                    //The first condition is not met
                    new AllowBlockCondition
                    {
                        Property = "Currency",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["EUR"],
                        PropertyType = Constants.PropertyTypes.String
                    },
                    //The conditions within the second condition are met
                    new AllowBlockCondition
                    {
                        LogicalOperator = "and",
                        Conditions =
                        [
                            new AllowBlockCondition
                            {
                                Property = "Amount",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["1000"],
                                PropertyType = Constants.PropertyTypes.Integer
                            },
                            new AllowBlockCondition
                            {
                                Property = "Customer.Contact.Email",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["<EMAIL>"],
                                PropertyType = Constants.PropertyTypes.String
                            }
                        ]
                    }
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule
                    .Conditions.Last()
                    .Conditions!.SelectMany(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = rule.Conditions.First().ToResultCondition()
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithNestedConditionsAndAndLogicalOperator_WhenAtLeastOneConditionIsNotMet_ReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                LogicalOperator = "and",
                Conditions =
                [
                    //The conditions within the first condition are met
                    new AllowBlockCondition
                    {
                        LogicalOperator = "or",
                        Conditions =
                        [
                            new AllowBlockCondition
                            {
                                Property = "Amount",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["1000"],
                                PropertyType = Constants.PropertyTypes.Integer
                            },
                            new AllowBlockCondition
                            {
                                Property = "Customer.Contact.Email",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["<EMAIL>"],
                                PropertyType = Constants.PropertyTypes.String
                            }
                        ]
                    },
                    //The last condition is not met
                    new AllowBlockCondition
                    {
                        Property = "Currency",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["EUR"],
                        PropertyType = Constants.PropertyTypes.String
                    },
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = false,
                MatchingConditions = rule
                    .Conditions.First()
                    .Conditions!.Take(1)
                    .SelectMany(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = rule.Conditions.Last().ToResultCondition()
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithNestedConditionsAndAndLogicalOperator_WhenAllConditionsAreMet_ReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                LogicalOperator = "and",
                Conditions =
                [
                    //The conditions within the first condition are met
                    new AllowBlockCondition
                    {
                        LogicalOperator = "or",
                        Conditions =
                        [
                            new AllowBlockCondition
                            {
                                Property = "Amount",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["1000"],
                                PropertyType = Constants.PropertyTypes.Integer
                            },
                            new AllowBlockCondition
                            {
                                Property = "Customer.Contact.Email",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["<EMAIL>"],
                                PropertyType = Constants.PropertyTypes.String
                            }
                        ]
                    },
                    //The last condition is met
                    new AllowBlockCondition
                    {
                        Property = "Currency",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["USD"],
                        PropertyType = Constants.PropertyTypes.String
                    },
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions =
                [
                    .. rule.Conditions.Last().ToResultCondition(),
                    .. rule
                        .Conditions.First()
                        .Conditions!.Take(1)
                        .SelectMany(c => c.ToResultCondition())
                ],
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithNestedConditionsAndOrLogicalOperator_WhenAllConditionsAreMet_ReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                LogicalOperator = "or",
                Conditions =
                [
                    //The conditions within the first condition are met
                    new AllowBlockCondition
                    {
                        LogicalOperator = "and",
                        Conditions =
                        [
                            new AllowBlockCondition
                            {
                                Property = "Amount",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["1000"],
                                PropertyType = Constants.PropertyTypes.Integer
                            },
                            new AllowBlockCondition
                            {
                                Property = "Customer.Contact.Email",
                                Operator = Constants.Operator.EqualsTo,
                                Values = ["<EMAIL>"],
                                PropertyType = Constants.PropertyTypes.String
                            }
                        ]
                    },
                    //The last condition is met
                    new AllowBlockCondition
                    {
                        Property = "Currency",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["USD"],
                        PropertyType = Constants.PropertyTypes.String
                    },
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule.Conditions.First().ToResultCondition(),
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }

    [Test]
    public void WithNestedNestedConditionsAndOrLogicalOperator_WhenAllConditionsAreMet_ReturnsCorrectResult()
    {
        Payment fact =
            new()
            {
                Id = "1",
                Status = "pending",
                Amount = 1000,
                Currency = "USD",
                Customer = new Customer
                {
                    FirstName = "John",
                    LastName = "Doe",
                    Contact = new Contact { Email = "<EMAIL>" }
                }
            };

        Rule rule =
            new()
            {
                Id = Guid.NewGuid().ToString(),
                LogicalOperator = "or",
                Conditions =
                [
                    //The conditions within the first condition are met
                    new AllowBlockCondition
                    {
                        LogicalOperator = "and",
                        Conditions =
                        [
                            new AllowBlockCondition()
                            {
                                LogicalOperator = "and",
                                Conditions =
                                [
                                    new AllowBlockCondition()
                                    {
                                        Property = "Amount",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["1000"],
                                        PropertyType = Constants.PropertyTypes.Integer
                                    },
                                    new AllowBlockCondition
                                    {
                                        Property = "Customer.Contact.Email",
                                        Operator = Constants.Operator.EqualsTo,
                                        Values = ["<EMAIL>"],
                                        PropertyType = Constants.PropertyTypes.String
                                    }
                                ]
                            }
                        ]
                    },
                    //The last condition is met
                    new AllowBlockCondition
                    {
                        Property = "Currency",
                        Operator = Constants.Operator.EqualsTo,
                        Values = ["USD"],
                        PropertyType = Constants.PropertyTypes.String
                    },
                ]
            };
        Result expectedResult =
            new()
            {
                IsMatch = true,
                MatchingConditions = rule
                    .Conditions.First()
                    .Conditions!.SelectMany(c => c.ToResultCondition())
                    .ToList(),
                NonMatchingConditions = []
            };
        _engine.Evaluate(rule, fact).Should().BeEquivalentTo(expectedResult);
    }
}
